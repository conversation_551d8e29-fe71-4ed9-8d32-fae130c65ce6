"""
MM-MeanReversion — production-ready controller using Hummingbot strategy_v2

This module implements the mean-reversion market-making logic outlined in the
user's spec. It is designed to live at:

  deploy/bots/controllers/market_making/mm_meanrev.py

and to be used with Hummingbot's strategy_v2 framework. It extends the
user-provided skeleton (MMMeanrevConfig, MMMeanrevController) with core quoting
logic, dynamic spread/refresh, inventory skew, spike-hedging hooks, rounding to
exchange filters, and emission of PositionExecutorConfig objects for two levels
per side.

Notes
-----
- The controller focuses on producing level requests (price, amount, level_id)
  and risk events. Position lifecycle (placement, cancels, post-only, etc.) is
  handled by the PositionExecutor layer and MarketMakingControllerBase helpers.
- Where framework specifics may differ by HB build, guarded fallbacks and
  explicit docstrings are provided. Search for "HOOK:" comments where
  integration points exist in your HB fork.
- By default, sizes are expressed in quote (FDUSD). Ensure you have sufficient
  free balances.
"""
from __future__ import annotations

from dataclasses import dataclass
from decimal import Decimal, getcontext
from math import ceil, floor, exp, log
from typing import Dict, Iterable, List, Optional, Tuple

from pydantic import Field, ConfigDict

from hummingbot.core.data_type.common import TradeType
from hummingbot.data_feed.candles_feed.data_types import CandlesConfig
from hummingbot.strategy_v2.controllers.market_making_controller_base import (
    MarketMakingControllerBase,
    MarketMakingControllerConfigBase,
)
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig

# Increase precision for price math
getcontext().prec = 28


# =========================
# CONFIG
# =========================
class MMMeanrevConfig(MarketMakingControllerConfigBase):
    model_config = ConfigDict(extra='allow')
    controller_name: str = "mm_meanrev"

    candles_config: List[CandlesConfig] = Field(default=[])

    # Spreads
    s_base: Decimal = Field(default=Decimal("0.0010"))  # total spread across mid (e.g., 0.10%)
    s_min: Decimal = Field(default=Decimal("0.0008"))
    s_max: Decimal = Field(default=Decimal("0.0030"))

    # Maker safety (additional margin beyond best bid/ask)
    maker_safety_margin: Decimal = Field(default=Decimal("0.0002"))  # 2 bps

    # Levels per side
    levels_per_side: int = Field(default=2)

    # Sizing (quote-based)
    use_quote_amount: bool = Field(default=True)
    order_amount_quote_l1: Decimal = Field(default=Decimal("15"))  # FDUSD
    order_amount_quote_l2: Decimal = Field(default=Decimal("30"))

    # Vol & cadence
    refresh_min_seconds: float = Field(default=1.0)
    refresh_max_seconds: float = Field(default=10.0)

    # Spike / hedge
    spike_pct: float = Field(default=0.01)
    spike_window_seconds: int = Field(default=60)
    allow_taker_on_hedge: bool = Field(default=True)

    # Inventory skewing
    imbalance_threshold: float = Field(default=0.30)
    skew_k: float = Field(default=0.08)

    # Order lifecycle
    entry_order_timeout_seconds: int = Field(default=2 * 60 * 60)

    # Optional risk wrappers for the executor
    triple_barrier_config: Optional[dict] = Field(default=None)

    leverage: Decimal = Field(default=Decimal("1"))

    # Mid smoothing (EMA half-life in seconds)
    mid_half_life_seconds: float = Field(default=3.0)

    # EWMA return memory (seconds) for volatility
    vol_memory_seconds: float = Field(default=45.0)

    # Reference volatility (long-run) used to scale spreads; if 0, it is auto-initialized
    sigma_ref_init: float = Field(default=0.0)


# =========================
# INTERNAL DATA CLASSES
# =========================
@dataclass
class Quotes:
    bid_l1: Decimal
    ask_l1: Decimal
    bid_l2: Decimal
    ask_l2: Decimal
    size_l1: Decimal
    size_l2: Decimal


# =========================
# CONTROLLER
# =========================
class MMMeanrevController(MarketMakingControllerBase):
    def __init__(self, config: MMMeanrevConfig, *args, **kwargs):
        super().__init__(config, *args, **kwargs)
        self.config: MMMeanrevConfig = config

        # State
        self._mid_ema: Optional[Decimal] = None
        self._ewma_r: Optional[float] = None
        self._ewma_r2: Optional[float] = None
        self._sigma_ref: Optional[float] = None
        self._last_mid: Optional[Decimal] = None
        self._last_refresh_s: float = float(self.config.refresh_max_seconds)

        # Spike window buffer (timestamp -> mid)
        self._spike_ring: List[Tuple[float, Decimal]] = []

    # ---------------------
    # CORE LOOP ENTRYPOINT
    # ---------------------
    def on_tick(self) -> List[PositionExecutorConfig]:
        """Main entrypoint invoked by the base controller each scheduling tick.

        Returns a list of PositionExecutorConfig objects that the strategy's
        executors will consume. This method:
          1) Updates mid, volatility, refresh interval, and dynamic spread.
          2) Computes inventory skew.
          3) Builds L1/L2 quotes per side, applies skew & maker safety, rounds.
          4) Emits executor configs for active levels.
          5) Triggers emergency hedge if spike detected with imbalance.
        """
        now_s = float(self.market_data_provider.time())

        best_bid, best_ask = self._get_top_of_book()
        if best_bid is None or best_ask is None:
            return []

        mid_raw = (best_bid + best_ask) / Decimal("2")
        mid = self._update_mid_ema(mid_raw, now_s)

        sigma = self._update_volatility(mid, now_s)
        t_refresh = self._compute_refresh_interval(sigma)
        s_current = self._compute_dynamic_spread(sigma)

        # Early out if kill-switch or cool-down is active
        if self.kill_switch_triggered:
            return []

        # Inventory & skew
        exposure = self._compute_exposure(mid)
        skew_pct = self._compute_skew_pct(exposure)

        # Build quotes
        quotes = self._build_quotes(mid, s_current, best_bid, best_ask, skew_pct)
        execs: List[PositionExecutorConfig] = []

        # Emit L1 per side
        if quotes.size_l1 > 0:
            execs.extend(
                self._emit_level_pair(
                    level_suffix="l1",
                    bid_price=quotes.bid_l1,
                    ask_price=quotes.ask_l1,
                    size_quote=self.config.order_amount_quote_l1,
                )
            )
        # Emit L2 per side
        if quotes.size_l2 > 0 and self.config.levels_per_side >= 2:
            execs.extend(
                self._emit_level_pair(
                    level_suffix="l2",
                    bid_price=quotes.bid_l2,
                    ask_price=quotes.ask_l2,
                    size_quote=self.config.order_amount_quote_l2,
                )
            )

        # Spike hedge (taker) if necessary
        hedge_exec = self._maybe_emergency_hedge(now_s, mid, exposure)
        if hedge_exec is not None:
            execs.append(hedge_exec)

        # Advise scheduler of desired refresh cadence
        self._advise_refresh_interval(t_refresh)

        return execs

    # ---------------------
    # QUOTES & LEVEL EMISSION
    # ---------------------
    def _emit_level_pair(
        self,
        level_suffix: str,
        bid_price: Decimal,
        ask_price: Decimal,
        size_quote: Decimal,
    ) -> List[PositionExecutorConfig]:
        """Create executor configs for a bid and an ask level with the given prices.

        Prices are assumed already rounded and maker-safe. Sizes are quote-based
        and converted per side to base quantity respecting lot size/min notional.
        """
        bid_qty = self._quote_to_base_qty(size_quote, bid_price)
        ask_qty = self._quote_to_base_qty(size_quote, ask_price)

        # Round quantities and enforce min_notional
        bid_price_r, bid_qty_r = self._round_and_validate(bid_price, bid_qty, side=TradeType.BUY)
        ask_price_r, ask_qty_r = self._round_and_validate(ask_price, ask_qty, side=TradeType.SELL)

        execs: List[PositionExecutorConfig] = []
        if bid_qty_r > Decimal("0"):
            execs.append(self.get_executor_config(level_id=f"bid_{level_suffix}", price=bid_price_r, amount=bid_qty_r))
        if ask_qty_r > Decimal("0"):
            execs.append(self.get_executor_config(level_id=f"ask_{level_suffix}", price=ask_price_r, amount=ask_qty_r))
        return execs

    def get_executor_config(self, level_id: str, price: Decimal, amount: Decimal) -> PositionExecutorConfig:
        trade_type = self.get_trade_type_from_level_id(level_id)
        return PositionExecutorConfig(
            timestamp=self.market_data_provider.time(),
            level_id=level_id,
            connector_name=self.config.connector_name,
            trading_pair=self.config.trading_pair,
            entry_price=price,
            amount=amount,
            triple_barrier_config=self.config.triple_barrier_config,
            leverage=self.config.leverage,
            side=trade_type,
            # HOOK: if your PositionExecutor supports maker-only flags or order types,
            # add them here, e.g., order_type=OrderType.LIMIT_MAKER
        )

    # ---------------------
    # MID, VOL, SPREAD, CADENCE
    # ---------------------
    def _update_mid_ema(self, mid_raw: Decimal, now_s: float) -> Decimal:
        """EMA smoothing of the mid with half-life from config.
        Uses the time since last refresh stored in self._last_refresh_s to compute alpha.
        """
        if self._mid_ema is None:
            self._mid_ema = mid_raw
            self._last_mid = mid_raw
            return mid_raw

        # Alpha from half-life and effective sampling interval (last refresh)
        dt = max(1e-6, self._last_refresh_s)
        T_half = max(1e-6, float(self.config.mid_half_life_seconds))
        alpha = Decimal(1 - exp(-log(2.0) * dt / T_half))
        self._mid_ema = alpha * mid_raw + (Decimal("1") - alpha) * self._mid_ema
        self._last_mid = mid_raw
        return self._mid_ema

    def _update_volatility(self, mid: Decimal, now_s: float) -> float:
        """EWMA of log returns to estimate short-term volatility."""
        if self._last_mid is None or self._last_mid <= 0:
            self._last_mid = mid
            return 0.0
        r = float((mid / self._last_mid).ln()) if hasattr(mid, "ln") else float((mid.ln() - self._last_mid.ln()))  # type: ignore
        # Fallback if Decimal.ln not available: use float log
        try:
            r = float((mid / self._last_mid).ln())  # type: ignore
        except Exception:
            r = float(log(float(mid) / float(self._last_mid)))

        lam = max(1e-6, min(0.999, self._lambda_from_seconds(self.config.vol_memory_seconds)))
        self._ewma_r = lam * r + (1 - lam) * (self._ewma_r or 0.0)
        self._ewma_r2 = lam * (r * r) + (1 - lam) * (self._ewma_r2 or 0.0)
        var = max(0.0, (self._ewma_r2 or 0.0) - (self._ewma_r or 0.0) ** 2)
        sigma = var ** 0.5
        if self._sigma_ref is None or self._sigma_ref == 0.0:
            # Initialize reference as a slow-moving copy
            self._sigma_ref = sigma if sigma > 0 else 1e-6
        else:
            self._sigma_ref = 0.99 * self._sigma_ref + 0.01 * max(1e-8, sigma)
        return sigma

    def _lambda_from_seconds(self, memory_seconds: float) -> float:
        # Interpret as per-tick decay using last refresh duration
        dt = max(1e-6, self._last_refresh_s)
        # Convert memory_seconds into an approximate lambda in (0,1). Larger memory -> larger lambda.
        # We map: lambda = exp(-dt / memory_seconds)
        return float(exp(-dt / max(1.0, memory_seconds)))

    def _compute_refresh_interval(self, sigma: float) -> float:
        t_min = float(self.config.refresh_min_seconds)
        t_max = float(self.config.refresh_max_seconds)
        k_vol = 0.05
        t_offset = 0.5
        # Avoid division by zero
        base = k_vol / max(1e-8, sigma) + t_offset
        t_refresh = max(t_min, min(t_max, base))
        self._last_refresh_s = t_refresh
        return t_refresh

    def _compute_dynamic_spread(self, sigma: float) -> Decimal:
        s_base = float(self.config.s_base)
        s_min = float(self.config.s_min)
        s_max = float(self.config.s_max)
        sigma_ref = float(self._sigma_ref or max(1e-8, sigma))
        c_vol = 1.0  # sensitivity; tune via backtests
        s = s_base * (1.0 + c_vol * (sigma / max(1e-8, sigma_ref)))
        s_clamped = max(s_min, min(s_max, s))
        return Decimal(str(s_clamped))

    # ---------------------
    # INVENTORY & SKEW
    # ---------------------
    def _compute_exposure(self, mid: Decimal) -> float:
        base_bal, quote_bal = self._get_balances()
        v_base = float(base_bal * mid)
        v_quote = float(quote_bal)
        pv = v_base + v_quote
        if pv <= 0:
            return 0.5
        return v_base / pv

    def _compute_skew_pct(self, exposure: float) -> float:
        deviation = exposure - 0.5
        if abs(deviation) < float(self.config.imbalance_threshold):
            return 0.0
        return float(self.config.skew_k) * deviation

    # ---------------------
    # QUOTE CONSTRUCTION
    # ---------------------
    def _build_quotes(
        self,
        mid: Decimal,
        s_current: Decimal,
        best_bid: Decimal,
        best_ask: Decimal,
        skew_pct: float,
    ) -> Quotes:
        # L1 symmetric around mid
        half = s_current / Decimal("2")
        bid_l1 = mid * (Decimal("1") - half)
        ask_l1 = mid * (Decimal("1") + half)

        # L2 wider
        alpha2 = Decimal("1.75")
        half2 = (s_current * alpha2) / Decimal("2")
        bid_l2 = mid * (Decimal("1") - half2)
        ask_l2 = mid * (Decimal("1") + half2)

        # Maker safety vs top-of-book
        m = self.config.maker_safety_margin
        bid_l1 = min(bid_l1, best_bid * (Decimal("1") - m))
        ask_l1 = max(ask_l1, best_ask * (Decimal("1") + m))
        bid_l2 = min(bid_l2, best_bid * (Decimal("1") - m))
        ask_l2 = max(ask_l2, best_ask * (Decimal("1") + m))

        # Inventory skew: if long (exposure>0.5), widen bids and narrow asks; opposite if short
        if skew_pct != 0.0:
            sign = 1 if skew_pct > 0 else -1
            adj = Decimal(abs(skew_pct))
            if sign > 0:  # long, favor selling
                bid_l1 *= (Decimal("1") + adj)
                ask_l1 *= (Decimal("1") - adj)
                bid_l2 *= (Decimal("1") + adj)
                ask_l2 *= (Decimal("1") - adj)
            else:  # short, favor buying
                bid_l1 *= (Decimal("1") - adj)
                ask_l1 *= (Decimal("1") + adj)
                bid_l2 *= (Decimal("1") - adj)
                ask_l2 *= (Decimal("1") + adj)

        # Round to filters
        bid_l1 = self._round_price(bid_l1, side=TradeType.BUY)
        ask_l1 = self._round_price(ask_l1, side=TradeType.SELL)
        bid_l2 = self._round_price(bid_l2, side=TradeType.BUY)
        ask_l2 = self._round_price(ask_l2, side=TradeType.SELL)

        return Quotes(
            bid_l1=bid_l1,
            ask_l1=ask_l1,
            bid_l2=bid_l2,
            ask_l2=ask_l2,
            size_l1=Decimal("1"),  # flag that L1 is active
            size_l2=Decimal("1"),  # flag that L2 is active
        )

    # ---------------------
    # SPIKE HEDGE
    # ---------------------
    def _maybe_emergency_hedge(
        self, now_s: float, mid: Decimal, exposure: float
    ) -> Optional[PositionExecutorConfig]:
        """Detect large short-term moves and, if imbalanced, emit a taker hedge.

        Implementation note: PositionExecutorConfig here reuses the same type,
        but your PositionExecutor must interpret a special level_id to place a
        market order (or you can implement a dedicated TakerHedgeExecutor in your
        codebase). See HOOK below.
        """
        # Maintain spike ring buffer
        self._spike_ring.append((now_s, mid))
        horizon = float(self.config.spike_window_seconds)
        while self._spike_ring and now_s - self._spike_ring[0][0] > horizon:
            self._spike_ring.pop(0)

        if len(self._spike_ring) < 2:
            return None

        start_t, start_mid = self._spike_ring[0]
        ret = float((mid - start_mid) / start_mid)
        if abs(ret) < float(self.config.spike_pct):
            return None

        # Require meaningful imbalance to hedge
        deviation = exposure - 0.5
        if abs(deviation) < float(self.config.imbalance_threshold) / 2.0:
            return None

        # Determine hedge direction & size: move exposure back to 0.5
        base_bal, quote_bal = self._get_balances()
        pv = float(base_bal * mid + quote_bal)
        target_base_value = 0.5 * pv
        current_base_value = float(base_bal * mid)
        delta_value = target_base_value - current_base_value
        if abs(delta_value) <= 0:
            return None

        # Convert to base qty
        qty = Decimal(str(abs(delta_value))) / mid
        # Round qty to lot size
        _, qty_r = self._round_and_validate(price=mid, qty=qty, side=(TradeType.BUY if delta_value > 0 else TradeType.SELL))
        if qty_r <= 0:
            return None

        # HOOK: mark as a taker hedge via special level_id understood by your executor
        level_id = "hedge_market_buy" if delta_value > 0 else "hedge_market_sell"
        return PositionExecutorConfig(
            timestamp=self.market_data_provider.time(),
            level_id=level_id,
            connector_name=self.config.connector_name,
            trading_pair=self.config.trading_pair,
            entry_price=mid,  # informational; executor should use MARKET
            amount=qty_r,
            triple_barrier_config=None,
            leverage=self.config.leverage,
            side=TradeType.BUY if delta_value > 0 else TradeType.SELL,
            # HOOK: set order_type=OrderType.MARKET or a custom flag in your executor
        )

    # ---------------------
    # EXCHANGE FILTERS & ROUNDING
    # ---------------------
    def _round_and_validate(self, price: Decimal, qty: Decimal, side: TradeType) -> Tuple[Decimal, Decimal]:
        price_r = self._round_price(price, side)
        lot = self.exchange.get_lot_size(self.config.connector_name, self.config.trading_pair)
        min_notional = self.exchange.get_min_notional(self.config.connector_name, self.config.trading_pair)
        qty_r = (qty / lot).to_integral_value(rounding="ROUND_FLOOR") * lot
        if qty_r <= 0:
            return price_r, Decimal("0")
        if price_r * qty_r < min_notional:
            # Try bumping quantity to hit min notional (if balance allows, base-side)
            needed = (min_notional / price_r)
            qty_r2 = (needed / lot).to_integral_value(rounding="ROUND_CEILING") * lot
            # Ensure we have balance
            base_bal, quote_bal = self._get_balances()
            if side is TradeType.BUY:
                # check quote balance
                cost = qty_r2 * price_r
                if cost <= quote_bal:
                    qty_r = qty_r2
                else:
                    return price_r, Decimal("0")
            else:
                # check base balance
                if qty_r2 <= base_bal:
                    qty_r = qty_r2
                else:
                    return price_r, Decimal("0")
        return price_r, qty_r

    def _round_price(self, price: Decimal, side: TradeType) -> Decimal:
        tick = self.exchange.get_tick_size(self.config.connector_name, self.config.trading_pair)
        if tick <= 0:
            return price
        if side is TradeType.BUY:
            steps = (price / tick).to_integral_value(rounding="ROUND_FLOOR")
        else:
            steps = (price / tick).to_integral_value(rounding="ROUND_CEILING")
        return steps * tick

    def _quote_to_base_qty(self, quote_amount: Decimal, price: Decimal) -> Decimal:
        if price <= 0:
            return Decimal("0")
        return quote_amount / price

    # ---------------------
    # HELPERS
    # ---------------------
    def _get_top_of_book(self) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        tob = self.market_data_provider.get_price_by_type(
            connector_name=self.config.connector_name,
            trading_pair=self.config.trading_pair,
            price_type="mid_best",  # HOOK: use framework's price types (best bid/ask might be separate calls)
        )
        # Fallback if mid_best not available: fetch bid/ask separately
        if isinstance(tob, tuple) and len(tob) == 2:
            best_bid, best_ask = tob
            return Decimal(str(best_bid)), Decimal(str(best_ask))
        try:
            best_bid = self.market_data_provider.get_best_bid(self.config.connector_name, self.config.trading_pair)
            best_ask = self.market_data_provider.get_best_ask(self.config.connector_name, self.config.trading_pair)
            return Decimal(str(best_bid)), Decimal(str(best_ask))
        except Exception:
            return None, None

    def _get_balances(self) -> Tuple[Decimal, Decimal]:
        base, quote = self._split_trading_pair()
        base_bal = Decimal(str(self.exchange.get_balance(self.config.connector_name, base)))
        quote_bal = Decimal(str(self.exchange.get_balance(self.config.connector_name, quote)))
        return base_bal, quote_bal

    def _split_trading_pair(self) -> Tuple[str, str]:
        if "-" in self.config.trading_pair:
            base, quote = self.config.trading_pair.split("-")
        elif "/" in self.config.trading_pair:
            base, quote = self.config.trading_pair.split("/")
        else:
            # Binance style e.g. DOGE-FDUSD is handled by dash above
            parts = [self.config.trading_pair[:3], self.config.trading_pair[3:]]
            base, quote = parts[0], parts[1]
        return base, quote

    def _advise_refresh_interval(self, t_refresh: float) -> None:
        """Advise scheduler about next desired refresh time.
        HOOK: If your HB build allows dynamic cadence at controller level, call
        self.set_next_refresh_in_seconds(t_refresh). Otherwise this is a no-op.
        """
        try:
            self.set_next_refresh_in_seconds(t_refresh)  # type: ignore[attr-defined]
        except Exception:
            pass

    # ---------------------
    # KILL SWITCH PROPERTY (proxy to base if available)
    # ---------------------
    @property
    def kill_switch_triggered(self) -> bool:
        try:
            return bool(self.is_kill_switch_triggered())  # type: ignore[attr-defined]
        except Exception:
            return False
