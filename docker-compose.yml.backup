services:
  dashboard:
    container_name: dashboard
    image: hummingbot/dashboard:latest
    ports:
      - "8501:8501"
    environment:
        - AUTH_SYSTEM_ENABLED=False
        - BACKEND_API_HOST=hummingbot-api
        - BACKEND_API_PORT=8000
        - BACKEND_API_USERNAME=admin
        - BACKEND_API_PASSWORD=admin
    volumes:
      - ./credentials.yml:/home/<USER>/credentials.yml
      - ./pages:/home/<USER>/frontend/pages
    networks:
        - emqx-bridge
  hummingbot-api:
    container_name: hummingbot-api
    image: hummingbot/hummingbot-api:latest
    ports:
      - "8000:8000"
    volumes:
      - ./bots:/hummingbot-api/bots
      - /var/run/docker.sock:/var/run/docker.sock
    env_file:
      - .env
    environment:
      # Override specific values for Docker networking
      - BROKER_HOST=emqx
      - DATABASE_URL=postgresql+asyncpg://hbot:hummingbot-api@postgres:5432/hummingbot_api
    networks:
      - emqx-bridge
    depends_on:
      - postgres
  emqx:
    container_name: hummingbot-broker
    image: emqx:5
    restart: unless-stopped
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=node1.emqx.local
      - EMQX_CLUSTER__DISCOVERY_STRATEGY=static
      - EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>]
      - EMQX_LOADED_PLUGINS="emqx_recon,emqx_retainer,emqx_management,emqx_dashboard"
    volumes:
      - emqx-data:/opt/emqx/data
      - emqx-log:/opt/emqx/log
      - emqx-etc:/opt/emqx/etc
    ports:
      - "1883:1883"  # mqtt:tcp
      - "8883:8883"  # mqtt:tcp:ssl
      - "8083:8083"  # mqtt:ws
      - "8084:8084"  # mqtt:ws:ssl
      - "8081:8081"  # http:management
      - "18083:18083"  # http:dashboard
      - "61613:61613"  # web-stomp gateway
    networks:
      emqx-bridge:
        aliases:
          - node1.emqx.local
    healthcheck:
      test: [ "CMD", "/opt/emqx/bin/emqx_ctl", "status" ]
      interval: 5s
      timeout: 25s
      retries: 5

  postgres:
    container_name: hummingbot-postgres
    image: postgres:15
    restart: unless-stopped
    environment:
      - POSTGRES_DB=hummingbot_api
      - POSTGRES_USER=hbot
      - POSTGRES_PASSWORD=hummingbot-api
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - emqx-bridge
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hbot -d hummingbot_api"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  emqx-bridge:
    driver: bridge

volumes:
  emqx-data: { }
  emqx-log: { }
  emqx-etc: { }
  postgres-data: { }
