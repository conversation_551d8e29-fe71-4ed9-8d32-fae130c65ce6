### Description

This page helps you deploy and manage Hummingbot instances:

- Starting and stopping Hummingbot Broker
- Creating, starting and stopping bot instances
- Managing strategy and script files that instances run
- Fetching status of running instances

### Maintainers

This page is maintained by Hummingbot Foundation as a template other pages:

* [cardosfede](https://github.com/cardosfede)
* [fengtality](https://github.com/fengtality)

### Wiki

See the [wiki](https://github.com/hummingbot/dashboard/wiki/%F0%9F%90%99-Bot-Orchestration) for more information.