import streamlit as st

from frontend.components.executors_distribution import get_executors_distribution_inputs
from frontend.components.risk_management import get_risk_management_inputs


def user_inputs():
    default_config = st.session_state.get("default_config", {})

    # Load existing or defaults
    # meanrev_window = default_config.get("meanrev_window", 20)
    # meanrev_threshold = default_config.get("meanrev_threshold", 0.001)
    # spread_min = default_config.get("spread_min", 0.001)
    # spread_max = default_config.get("spread_max", 0.005)
    # max_inventory_pct = default_config.get("max_inventory_pct", 0.1)
    # enable_spike_protection = default_config.get("enable_spike_protection", True)

    # General inputs (connector, pair, leverage, total quote, etc.)

    # Distribution inputs (spread tiers and order sizes)
    # buy_spread_distributions, sell_spread_distributions, buy_order_amounts_pct, \
    #     sell_order_amounts_pct = get_executors_distribution_inputs()

    # Risk management inputs (SL, TP, trailing stop, etc.)
    # sl, tp, time_limit, ts_ap, ts_delta, take_profit_order_type = get_risk_management_inputs()

    with st.expander("General Parameters", expanded=True):
        c1, c2, c3 = st.columns(3)
        with c1:
            exchange_name = st.text_input("Exchange Name", value="binance")
            trading_pair = st.text_input("Trading Pair", value="DOGE-FDUSD")
        with c2:
            order_amount = st.number_input("Order Amount", value=0.001)
            max_trade_notional = st.number_input(
                "Max Trade Notional", value=100.0)
        with c3:
            # options = ["1m", "3m", "5m", "15m", "30m",
            #            "1h", "2h", "4h", "6h", "12h", "1d"]
            tick_interval = st.number_input("Tick Interval in s`", value=0.5)
            order_refresh_time = st.number_input(
                "Order Refresh Time", value=3.0)

    # Scalper v2 settings
    with st.expander("Scalper V2 Parameters", expanded=True):
        c1, c2, c3, c4, c5 = st.columns(5)
        with c1:
            tp_spread = st.number_input("Take Profit Spread (%)", value=0.0015)
            sl_spread = st.number_input("Stop Loss Spread (%)", value=0.001)
        with c2:
            maker_fee = st.number_input("Maker Fee", value=0.0002)
            taker_fee = st.number_input("Taker Fee", value=0.0007)
        with c3:
            ob_levels = st.number_input("Order Book Levels", value=5)
            trade_window_s = st.number_input(
                "Trade Flow Window (s)", value=2.0)
        with c4:
            min_imbalance_ratio = st.number_input(
                "Min Imbalance Ratio", value=1.5)
            min_flow_ratio = st.number_input("Min Flow Ratio", value=1.2)
        with c5:
            state_save_interval = st.number_input(
                "State Save Interval (s)", value=5.0)

    with st.expander("Risk Management", expanded=True):
        c1, c2, c3, c4 = st.columns(4)
        with c1:
            max_position_base = st.number_input("Max Position Base", value=1.0)
            max_position_quote = st.number_input(
                "Max Position Quote", value=1000.0)
        with c2:
            max_daily_loss = st.number_input("Max Daily Loss", value=50.0)
            max_drawdown_pct = st.number_input("Max Drawdown (%)", value=0.03)
        with c3:
            position_size_pct = st.number_input(
                "Position Size (%)", value=0.02)
            max_open_orders = st.number_input("Max Open Orders", value=5)
        with c4:
            cooldown_after_loss_s = st.number_input(
                "Cooldown After Loss (s)", value=30.0)
            emergency_exit_spread = st.number_input(
                "Emergency Exit Spread (%)", value=0.002)

    # Build config dict
    config = {
        "id": default_config.get("id", "scalper_v2_0.1"),
        "controller_name": "scalper_v2",
        "controller_type": "scalping",
        "exchange": exchange_name,
        "trading_pair": trading_pair,
        "tick_interval": tick_interval,
        "order_amount": order_amount,
        "max_trade_notional": max_trade_notional,
        "order_refresh_time": order_refresh_time,
        "tp_spread": tp_spread,
        "sl_spread": sl_spread,
        "maker_fee": maker_fee,
        "taker_fee": taker_fee,
        "ob_levels": ob_levels,
        "trade_window_s": trade_window_s,
        "min_imbalance_ratio": min_imbalance_ratio,
        "min_flow_ratio": min_flow_ratio,
        "state_save_interval": state_save_interval,
        "risk": {
            "max_position_base": max_position_base,
            "max_position_quote": max_position_quote,
            "max_daily_loss": max_daily_loss,
            "max_drawdown_pct": max_drawdown_pct,
            "position_size_pct": position_size_pct,
            "max_open_orders": max_open_orders,
            "cooldown_after_loss_s": cooldown_after_loss_s,
            "emergency_exit_spread": emergency_exit_spread,
        }
    }

    return config
