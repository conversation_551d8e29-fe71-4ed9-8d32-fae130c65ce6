"""Enhanced signal generation with noise filtering and regime awareness."""
import time
from collections import deque
from decimal import Decimal
from typing import Deque, Optional, Dict, Any
from .types import SignalType
import statistics

class MarketDataBuffer:
    """Efficient circular buffer for market data."""
    
    def __init__(self, max_size: int = 1000):
        self.trades: Deque = deque(maxlen=max_size)
        self.spreads: Deque = deque(maxlen=max_size) 
        self.imbalances: Deque = deque(maxlen=max_size)
        
    def add_trade(self, price: float, amount: float, side: str, timestamp: float):
        self.trades.append((timestamp, price, amount, side))
        
    def add_spread(self, spread: Decimal, timestamp: float):
        self.spreads.append((timestamp, spread))
        
    def add_imbalance(self, ratio: float, timestamp: float):
        self.imbalances.append((timestamp, ratio))

class EnhancedSignalGenerator:
    """Production-grade signal generation with multiple filters."""
    
    def __init__(self, config):
        self.config = config
        self.data_buffer = MarketDataBuffer()
        self._last_signal_time = 0.0
        self._signal_cooldown = 0.1  # Minimum time between signals
        
    def generate_signal(self, 
                       orderbook_snapshot: Dict[str, Any],
                       recent_trades: list,
                       current_spread: Decimal) -> tuple[SignalType, float]:
        """Generate trading signal with confidence score."""
        
        now = time.time()
        
        # Rate limiting
        if now - self._last_signal_time < self._signal_cooldown:
            return SignalType.HOLD, 0.0
            
        # Update buffers
        self._update_buffers(orderbook_snapshot, recent_trades, current_spread, now)
        
        # Generate base signals
        imbalance_signal, imb_confidence = self._order_book_imbalance_signal(orderbook_snapshot)
        flow_signal, flow_confidence = self._trade_flow_signal(recent_trades, now)
        regime_signal, regime_confidence = self._market_regime_signal(current_spread)
        
        # Combine signals with weighted confidence
        signals = [
            (imbalance_signal, imb_confidence * 0.4),
            (flow_signal, flow_confidence * 0.3), 
            (regime_signal, regime_confidence * 0.3)
        ]
        
        # Aggregate decision
        buy_weight = sum(conf for sig, conf in signals if sig == SignalType.BUY)
        sell_weight = sum(conf for sig, conf in signals if sig == SignalType.SELL)
        
        # Decision thresholds
        min_confidence = 0.6
        if buy_weight > sell_weight and buy_weight >= min_confidence:
            self._last_signal_time = now
            return SignalType.BUY, buy_weight
        elif sell_weight > buy_weight and sell_weight >= min_confidence:
            self._last_signal_time = now
            return SignalType.SELL, sell_weight
            
        return SignalType.HOLD, max(buy_weight, sell_weight)
        
    def _update_buffers(self, orderbook: Dict, trades: list, spread: Decimal, timestamp: float):
        """Update internal data buffers."""
        # Add spread data
        self.data_buffer.add_spread(spread, timestamp)
        
        # Add trade data
        for trade in trades:
            self.data_buffer.add_trade(
                trade.get('price', 0.0),
                trade.get('amount', 0.0), 
                trade.get('side', ''),
                trade.get('timestamp', timestamp)
            )
            
        # Calculate and store imbalance
        if 'bids' in orderbook and 'asks' in orderbook:
            bid_depth = sum(level[1] for level in orderbook['bids'][:self.config.ob_levels])
            ask_depth = sum(level[1] for level in orderbook['asks'][:self.config.ob_levels])
            if ask_depth > 0:
                imbalance_ratio = bid_depth / ask_depth
                self.data_buffer.add_imbalance(imbalance_ratio, timestamp)
                
    def _order_book_imbalance_signal(self, orderbook: Dict) -> tuple[SignalType, float]:
        """Enhanced order book imbalance with noise filtering."""
        if not orderbook.get('bids') or not orderbook.get('asks'):
            return SignalType.HOLD, 0.0
            
        # Calculate weighted imbalance using top N levels
        bid_depth = sum(level[1] for level in orderbook['bids'][:self.config.ob_levels])
        ask_depth = sum(level[1] for level in orderbook['asks'][:self.config.ob_levels])
        
        if ask_depth == 0:
            return SignalType.BUY, 0.8 if bid_depth > 0 else 0.0
            
        imbalance_ratio = bid_depth / ask_depth
        
        # Use historical volatility for dynamic thresholds
        threshold_multiplier = self._get_volatility_adjustment()
        buy_threshold = float(self.config.min_imbalance_ratio) * threshold_multiplier
        sell_threshold = 1.0 / buy_threshold
        
        if imbalance_ratio >= buy_threshold:
            confidence = min(0.9, (imbalance_ratio - buy_threshold) / buy_threshold + 0.5)
            return SignalType.BUY, confidence
        elif imbalance_ratio <= sell_threshold:
            confidence = min(0.9, (sell_threshold - imbalance_ratio) / sell_threshold + 0.5)
            return SignalType.SELL, confidence
            
        return SignalType.HOLD, 0.3
        
    def _trade_flow_signal(self, recent_trades: list, current_time: float) -> tuple[SignalType, float]:
        """Trade flow pressure with time decay."""
        if not recent_trades:
            return SignalType.HOLD, 0.0
            
        # Filter recent trades within window
        cutoff_time = current_time - self.config.trade_window_s
        relevant_trades = [t for t in self.data_buffer.trades if t[0] >= cutoff_time]
        
        if not relevant_trades:
            return SignalType.HOLD, 0.0
            
        # Calculate volume-weighted flow with time decay
        buy_flow = Decimal("0")
        sell_flow = Decimal("0")
        
        for timestamp, price, amount, side in relevant_trades:
            # Time decay factor (more recent trades have higher weight)
            time_weight = 1.0 - (current_time - timestamp) / self.config.trade_window_s
            weighted_amount = Decimal(str(amount)) * Decimal(str(time_weight))
            
            if side.lower() == 'buy':
                buy_flow += weighted_amount
            else:
                sell_flow += weighted_amount
                
        if sell_flow == 0:
            return SignalType.BUY, 0.7 if buy_flow > 0 else 0.0
            
        flow_ratio = float(buy_flow / sell_flow)
        min_ratio = float(self.config.min_flow_ratio)
        
        if flow_ratio >= min_ratio:
            confidence = min(0.8, (flow_ratio - min_ratio) / min_ratio * 0.3 + 0.4)
            return SignalType.BUY, confidence
        elif flow_ratio <= 1.0 / min_ratio:
            confidence = min(0.8, ((1.0 / min_ratio) - flow_ratio) / (1.0 / min_ratio) * 0.3 + 0.4)
            return SignalType.SELL, confidence
            
        return SignalType.HOLD, 0.2
        
    def _market_regime_signal(self, current_spread: Decimal) -> tuple[SignalType, float]:
        """Market regime awareness - avoid trading in unfavorable conditions."""
        
        # Check if spread is too wide (low liquidity)
        if current_spread > self.config.min_spread * Decimal("3"):
            return SignalType.HOLD, 0.0  # Don't trade in illiquid conditions
            
        # Check recent spread volatility
        if len(self.data_buffer.spreads) >= 10:
            recent_spreads = [s[1] for s in list(self.data_buffer.spreads)[-10:]]
            spread_volatility = Decimal(str(statistics.stdev(float(s) for s in recent_spreads)))
            
            # High spread volatility indicates unstable market
            if spread_volatility > current_spread * Decimal("0.5"):
                return SignalType.HOLD, 0.1
                
        # Favorable conditions
        return SignalType.HOLD, 0.8
        
    def _get_volatility_adjustment(self) -> float:
        """Adjust signal thresholds based on market volatility."""
        if len(self.data_buffer.spreads) < 20:
            return 1.0
            
        recent_spreads = [float(s[1]) for s in list(self.data_buffer.spreads)[-20:]]
        if not recent_spreads:
            return 1.0
            
        try:
            volatility = statistics.stdev(recent_spreads)
            mean_spread = statistics.mean(recent_spreads)
            cv = volatility / mean_spread if mean_spread > 0 else 0
            
            # Higher volatility requires higher thresholds
            return max(0.8, min(1.5, 1.0 + cv))
        except:
            return 1.0
