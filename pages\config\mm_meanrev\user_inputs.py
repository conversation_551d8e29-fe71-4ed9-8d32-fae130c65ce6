import streamlit as st

from frontend.components.executors_distribution import get_executors_distribution_inputs
from frontend.components.market_making_general_inputs import get_market_making_general_inputs
from frontend.components.risk_management import get_risk_management_inputs


def user_inputs():
    default_config = st.session_state.get("default_config", {})

    # Load existing or defaults
    meanrev_window = default_config.get("meanrev_window", 20)
    meanrev_threshold = default_config.get("meanrev_threshold", 0.001)
    spread_min = default_config.get("spread_min", 0.001)
    spread_max = default_config.get("spread_max", 0.005)
    max_inventory_pct = default_config.get("max_inventory_pct", 0.1)
    enable_spike_protection = default_config.get("enable_spike_protection", True)

    # General inputs (connector, pair, leverage, total quote, etc.)
    connector_name, trading_pair, leverage, total_amount_quote, position_mode, cooldown_time, \
        executor_refresh_time, _, _, interval = get_market_making_general_inputs(custom_candles=True)

    # Distribution inputs (spread tiers and order sizes)
    buy_spread_distributions, sell_spread_distributions, buy_order_amounts_pct, \
        sell_order_amounts_pct = get_executors_distribution_inputs()

    # Risk management inputs (SL, TP, trailing stop, etc.)
    sl, tp, time_limit, ts_ap, ts_delta, take_profit_order_type = get_risk_management_inputs()

    # Mean reversion settings
    with st.expander("Mean Reversion Parameters", expanded=True):
        c1, c2 = st.columns(2)
        with c1:
            meanrev_window = st.number_input(
                "Mean Reversion Window (candles)",
                min_value=1, max_value=500, value=meanrev_window, step=1,
                help="Window size for moving average used in mean reversion signal."
            )
            meanrev_threshold = st.number_input(
                "Mean Reversion Threshold (%)",
                min_value=0.0, max_value=5.0, value=meanrev_threshold * 100, step=0.05,
                help="Threshold (% deviation from mean) to trigger entry."
            ) / 100
        with c2:
            spread_min = st.number_input(
                "Min Spread (%)",
                min_value=0.0, max_value=5.0, value=spread_min * 100, step=0.05,
                help="Minimum bid/ask spread."
            ) / 100
            spread_max = st.number_input(
                "Max Spread (%)",
                min_value=0.0, max_value=10.0, value=spread_max * 100, step=0.1,
                help="Maximum bid/ask spread."
            ) / 100
            max_inventory_pct = st.number_input(
                "Max Inventory (% of quote balance)",
                min_value=0.0, max_value=100.0, value=max_inventory_pct * 100, step=1.0,
                help="Maximum fraction of portfolio to hold in base before skewing orders."
            ) / 100

    # Spike protection toggle
    enable_spike_protection = st.checkbox(
        "Enable Spike Protection",
        value=enable_spike_protection,
        help="Detect sudden volatility spikes and trigger emergency hedging."
    )

    # Build config dict
    config = {
        "id": default_config.get("id", "mm_meanrev_0.1"),
        "controller_name": "mm_meanrev",
        "controller_type": "market_making",
        "manual_kill_switch": default_config.get("manual_kill_switch", False),
        "candles_config": default_config.get("candles_config", []),
        "connector_name": connector_name,
        "trading_pair": trading_pair,
        "total_amount_quote": total_amount_quote,
        "executor_refresh_time": executor_refresh_time,
        "cooldown_time": cooldown_time,
        "leverage": leverage,
        "position_mode": position_mode,
        "candles_connector": connector_name,
        "candles_trading_pair": trading_pair,
        "interval": interval,
        "buy_spreads": buy_spread_distributions,
        "sell_spreads": sell_spread_distributions,
        "buy_amounts_pct": buy_order_amounts_pct,
        "sell_amounts_pct": sell_order_amounts_pct,
        "stop_loss": sl,
        "take_profit": tp,
        "time_limit": time_limit,
        "take_profit_order_type": take_profit_order_type.value,
        "trailing_stop": {
            "activation_price": ts_ap,
            "trailing_delta": ts_delta,
        },
        "meanrev_window": meanrev_window,
        "meanrev_threshold": meanrev_threshold,
        "spread_min": spread_min,
        "spread_max": spread_max,
        "max_inventory_pct": max_inventory_pct,
        "enable_spike_protection": enable_spike_protection,
    }

    return config
