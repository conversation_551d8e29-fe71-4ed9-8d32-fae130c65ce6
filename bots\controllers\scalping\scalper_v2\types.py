"""Type definitions for the scalping strategy."""
from enum import Enum
from decimal import Decimal
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
import time

class OrderStatus(Enum):
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELED = "canceled"
    FAILED = "failed"

class PositionSide(Enum):
    LONG = "long"
    SHORT = "short"
    NEUTRAL = "neutral"

class SignalType(Enum):
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"

@dataclass
class Position:
    """Current position state."""
    base_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    quote_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    unrealized_pnl: Decimal = field(default_factory=lambda: Decimal("0"))
    realized_pnl: Decimal = field(default_factory=lambda: Decimal("0"))
    avg_entry_price: Optional[Decimal] = None
    last_update: float = field(default_factory=time.time)
    
    @property
    def side(self) -> PositionSide:
        if self.base_amount > Decimal("0.0001"):
            return PositionSide.LONG
        elif self.base_amount < Decimal("-0.0001"):
            return PositionSide.SHORT
        return PositionSide.NEUTRAL
        
    @property
    def notional_value(self) -> Decimal:
        return abs(self.base_amount * (self.avg_entry_price or Decimal("0")))

@dataclass 
class OrderState:
    """Enhanced order tracking."""
    client_order_id: str
    exchange_order_id: Optional[str] = None
    side: str = ""
    amount: Decimal = field(default_factory=lambda: Decimal("0"))
    price: Decimal = field(default_factory=lambda: Decimal("0"))
    filled_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    status: OrderStatus = OrderStatus.PENDING
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    fees_paid: Decimal = field(default_factory=lambda: Decimal("0"))
    avg_fill_price: Optional[Decimal] = None
    
    @property
    def is_active(self) -> bool:
        return self.status in [OrderStatus.OPEN, OrderStatus.PARTIALLY_FILLED]
        
    @property
    def remaining_amount(self) -> Decimal:
        return self.amount - self.filled_amount

@dataclass
class RiskMetrics:
    """Risk tracking metrics."""
    daily_pnl: Decimal = field(default_factory=lambda: Decimal("0"))
    daily_volume: Decimal = field(default_factory=lambda: Decimal("0"))
    max_position_reached: Decimal = field(default_factory=lambda: Decimal("0"))
    total_fees_paid: Decimal = field(default_factory=lambda: Decimal("0"))
    win_rate: float = 0.0
    profit_factor: float = 1.0
    max_drawdown: Decimal = field(default_factory=lambda: Decimal("0"))
    consecutive_losses: int = 0
    last_trade_time: float = field(default_factory=time.time)
    is_emergency_mode: bool = False
    cooldown_until: float = 0.0
