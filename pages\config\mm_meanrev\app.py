import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

from frontend.components.backtesting import backtesting_section
from frontend.components.config_loader import get_default_config_loader
from frontend.components.save_config import render_save_config
from frontend.pages.config.mm_meanrev.user_inputs import user_inputs
from frontend.st_utils import get_backend_api_client, initialize_st_page
from frontend.visualization.backtesting import create_backtesting_figure
from frontend.visualization.backtesting_metrics import (
    render_accuracy_metrics,
    render_backtesting_metrics,
    render_close_types,
)
from frontend.visualization import theme
from frontend.visualization.candles import get_candlestick_trace
from frontend.pages.config.utils import get_candles
from frontend.visualization.executors_distribution import create_executors_distribution_traces
from frontend.visualization.indicators import get_macd_traces
from frontend.visualization.utils import add_traces_to_fig

# Initialize the Streamlit page
initialize_st_page(title="MM Meanrev", icon="📊")
backend_api_client = get_backend_api_client()

# Page description
st.text(
    "This tool lets you configure the MM Mean Reversion controller, "
    "run a backtest, and save/upload the config."
)

# Load default config for mm_meanrev
get_default_config_loader("mm_meanrev")

# Collect inputs
inputs = user_inputs()
st.session_state["default_config"].update(inputs)

days_to_visualize = st.number_input("Days to Visualize", min_value=1, max_value=365, value=7)
# Load candle data
candles = get_candles(connector_name=inputs["connector_name"], trading_pair=inputs["trading_pair"],
                      interval=inputs["interval"], days=days_to_visualize)
with st.expander("Visualizing PMM Dynamic Indicators", expanded=True):
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True,
                        subplot_titles=("Candlestick with Bollinger Bands"),)
    add_traces_to_fig(fig, [get_candlestick_trace(candles)], row=1, col=1)
    # add_traces_to_fig(fig, get_macd_traces(df=candles, macd_fast=inputs["macd_fast"], macd_slow=inputs["macd_slow"],
    #                                        macd_signal=inputs["macd_signal"]), row=2, col=1)
    # price_multiplier, spreads_multiplier = get_pmm_dynamic_multipliers(candles, inputs["macd_fast"],
                                                                    #    inputs["macd_slow"], inputs["macd_signal"],
                                                                    #    inputs["natr_length"])

    # add_traces_to_fig(fig,
    #                   [go.Scatter(x=candles.index, y=spreads_multiplier, name="Base Spread", line=dict(color="red"))],
    #                   row=4, col=1)
    fig.update_layout(**theme.get_default_layout(height=1000))
    # fig.update_yaxes(tickformat=".2%", row=3, col=1)
    # fig.update_yaxes(tickformat=".2%", row=4, col=1)
    st.plotly_chart(fig, use_container_width=True)

# Visualization: executor distribution
with st.expander("Executor Distribution:", expanded=True):
    fig = create_executors_distribution_traces(
        inputs["buy_spreads"],
        inputs["sell_spreads"],
        inputs["buy_amounts_pct"],
        inputs["sell_amounts_pct"],
        inputs["total_amount_quote"],
    )
    st.plotly_chart(fig, use_container_width=True)

# Backtesting section
bt_results = backtesting_section(inputs, backend_api_client)
if bt_results:
    fig = create_backtesting_figure(
        df=bt_results["processed_data"],
        executors=bt_results["executors"],
        config=inputs,
    )
    c1, c2 = st.columns([0.9, 0.1])
    with c1:
        render_backtesting_metrics(bt_results["results"])
        st.plotly_chart(fig, use_container_width=True)
    with c2:
        render_accuracy_metrics(bt_results["results"])
        st.write("---")
        render_close_types(bt_results["results"])

st.write("---")
render_save_config(
    st.session_state["default_config"]["id"],
    st.session_state["default_config"],
)
