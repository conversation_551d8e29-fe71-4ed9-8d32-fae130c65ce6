"""Enhanced scalper configuration with production-grade risk controls."""
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any
import os
# from hummingbot.strategy_v2.strategy_v2_config_base import StrategyV2ConfigBase

from hummingbot.strategy_v2.controllers.controller_base import ControllerBase, ControllerConfigBase


class RiskConfig(BaseModel):
    """Risk management configuration."""
    max_position_base: Decimal = Field(Decimal("1.0"), description="Maximum net base position")
    max_position_quote: Decimal = Field(Decimal("1000"), description="Maximum net quote position")
    max_daily_loss: Optional[Decimal] = Field(None, description="Daily loss limit (kills strategy)")
    max_drawdown_pct: Decimal = Field(Decimal("0.05"), description="Max drawdown % before pause")
    position_size_pct: Decimal = Field(Decimal("0.02"), description="Position size as % of available balance")
    max_open_orders: int = Field(10, description="Maximum concurrent open orders")
    cooldown_after_loss_s: float = Field(30.0, description="Cooldown after losing trade")
    emergency_exit_spread: Decimal = Field(Decimal("0.002"), description="Emergency market exit spread")
    
class ScalperV2ControllerConfig(ControllerConfigBase):
    # hummingbot config
    # id: str = Field(..., description="Hummingbot config ID")
    # controller_name: str = Field(..., description="Hummingbot controller name")
    # controller_type: str = Field(..., description="Hummingbot controller type")

    # Exchange & market
    exchange: str = Field(..., description="Hummingbot connector name")
    trading_pair: str = Field(..., description="Trading pair, e.g. 'BTC-USDT'")
    
    # Core strategy
    order_amount: Decimal = Field(..., description="Base order amount")
    max_trade_notional: Optional[Decimal] = Field(None, description="Max quote notional per trade")
    tick_interval: float = Field(0.5, description="Main strategy tick interval")
    order_refresh_time: float = Field(3.0, description="Order refresh interval")
    
    # Pricing
    min_spread: Decimal = Field(Decimal("0.0002"), description="Minimum profitable spread")
    tp_spread: Decimal = Field(Decimal("0.001"), description="Take profit spread")
    sl_spread: Decimal = Field(Decimal("0.001"), description="Stop loss spread")
    
    # Fees
    maker_fee: Decimal = Field(Decimal("0.0002"), description="Maker fee rate")
    taker_fee: Decimal = Field(Decimal("0.0007"), description="Taker fee rate")
    
    # Signal parameters
    ob_levels: int = Field(5, description="Order book levels for imbalance")
    trade_window_s: float = Field(2.0, description="Trade flow window")
    min_imbalance_ratio: Decimal = Field(Decimal("1.5"), description="Min imbalance for signal")
    min_flow_ratio: Decimal = Field(Decimal("1.2"), description="Min flow ratio for signal")
    
    # Risk management
    risk: RiskConfig = Field(default_factory=RiskConfig)
    
    # State management
    db_dsn: str = Field(
        default_factory=lambda: os.getenv("DATABASE_URL", "postgresql://user:pass@localhost:5432/scalper"),
        description="Database connection string"
    )
    state_save_interval: float = Field(5.0, description="State persistence interval")
    
    class Config:
        arbitrary_types_allowed = True
        
    @field_validator("trading_pair")
    def validate_pair(cls, v):
        if not any(sep in v for sep in ["-", "/"]):
            raise ValueError("trading_pair should contain '-' or '/' separator")
        return v
