# Market-Making + Mean-Reversion Hybrid for DOGE-FDUSD (detailed design & implementation guidelines)

This document describes a production-ready strategy that combines tight market-making quoting with mean-reversion bias & spike-hedging to capture many small maker profits on DOGE-FDUSD (Binance Spot). It contains clear mathematical formulas, state-machine logic, risk rules, monitoring/KPIs, backtest guidance, and an operational checklist — everything you need to implement safely (but no source code).

## 1) Strategy summary (one paragraph)

Quote two-sided limit orders near mid-price with dynamic spreads and two levels per side. Use smoothed mid-price as an anchor. Bias quotes toward mean reversion by skewing sizes/prices when the short-term price deviates from a moving average. Protect inventory with skew-based spread adjustments, and on extreme price spikes perform an emergency taker hedge to neutralize exposure. Dynamically adjust refresh cadence by volatility so orders are not stale in fast markets.

## 2) Key design variables (defaults to start; tune by backtest/paper)

Pair: DOGE-FDUSD (Binance Spot)

Quote sizing: 1% of available capital per order level (configurable)

Levels: 2 bid levels (L1, L2) and 2 ask levels (L1, L2)

Base spread (static baseline): s_base = 0.0010 (0.10%) — total spread across mid (i.e., ±0.05% around mid if symmetric)

Spread dynamic band: s_min = 0.0008, s_max = 0.0030 (0.08%–0.30%)

Maker safety margin: m_maker = 0.0002 (0.02% / 2 bps) to avoid immediate crossing

Refresh cadence bounds: t_min = 1s, t_max = 10s (dynamic)

Spike hedge threshold: spike_pct = 0.01 (1% move in T_spike_window), emergency hedge executed as market order

Mid smoothing EMA half-life: τ_mid ≈ 3s (or use short-window EMA of mid-price)

Volatility window & measure: use 1-minute ATR or EWMA of mid returns; see formulas below

Inventory imbalance threshold: imbalance_max = 0.30 (30% of total account value in base asset exposure triggers skew behavior)

Max daily loss / kill-switch: max_daily_loss = 0.02 (2% of capital)

Use these defaults as a starting point; they will be tuned.

## 3) Price, mid-price, and smoothing formulas
raw mid-price

Let best_bid and best_ask be current top-of-book prices.

mid_raw = (best_bid + best_ask) / 2

smoothed mid-price (EMA)

Use an exponential moving average with decay α_mid for time-continuous updates:

mid_smoothed_t = α_mid * mid_raw_t + (1 - α_mid) * mid_smoothed_{t-1}


Pick α_mid from refresh cadence: if you update every Δt seconds and want a smoothing half-life T_half (seconds),

α_mid = 1 - exp(-ln(2) * Δt / T_half)


E.g., T_half = 3s, Δt = 2s → α_mid ≈ small; effective smoothing avoids whipsaw.

## 4) Volatility, refresh cadence & dynamic spread
short-term mid returns EWMA volatility

Define log-return over interval Δt_i: r_i = ln(mid_i / mid_{i-1}). Compute EWMA std dev:

σ_t = sqrt( EWMA(r^2)_t - (EWMA(r)_t)^2 )
EWMA(x)_t = λ * x_t + (1 - λ) * EWMA(x)_{t-1}


Choose λ according to desired memory (e.g., corresponding to 30s–1min).

ATR style alternative (price range)

For candlestick-based ATR over N minutes:

TR_i = max(high_i - low_i, abs(high_i - close_{i-1}), abs(low_i - close_{i-1}))
ATR_N = SMA(TR, N)   (or use EWMA)

dynamic refresh cadence t_refresh

Map volatility to refresh interval (lower vol → longer refresh):

t_refresh = clamp( k_vol / σ_t + t_min_offset, t_min, t_max )


Where k_vol and t_min_offset are tuning constants. Example:

Let k_vol = 0.05, t_min_offset = 0.5s. If σ_t is large → smaller t_refresh; if σ_t small → larger t_refresh.

dynamic spread s_current

Map volatility to spread:

s_current = clamp( s_base * (1 + c_vol * (σ_t / σ_ref)), s_min, s_max )


σ_ref is reference volatility (e.g., long-run avg), c_vol scales sensitivity.

This makes spread widen when volatility rises.

## 5) Quoting logic (prices & sizes)
symmetric mid-anchored quotes (base)

Let mid = mid_smoothed. For a symmetric spread s the 1st level offsets are:

bid_L1 = mid * (1 - s/2 - δ1)
ask_L1 = mid * (1 + s/2 + δ1)


Where δ1 is a small tweak to ensure maker-only (using m_maker):

For bid, we want to be strictly below best bid to be a maker (or use LIMIT_MAKER). Safest to ensure ask/bid do not cross:

Simpler maker-safety adjustment:

bid_price = min(bid_L1, best_bid * (1 - m_maker))
ask_price = max(ask_L1, best_ask * (1 + m_maker))


Important: Quantize prices to tick size (see section 7).

Level 2 (further out)

Choose larger offset:

spread_L2 = s * α2   (e.g., α2 = 1.5 to 2.0)
bid_L2 = mid * (1 - spread_L2/2)
ask_L2 = mid * (1 + spread_L2/2)

size per level (base on risk/capital)

Let C_quote be how much quote currency (FDUSD) you allocate per order. Convert to base asset amount:

size_base = C_quote / price


Recommended sizing:

L1 size = f1 * C_total (e.g., 0.5% of capital)

L2 size = f2 * C_total (e.g., 1.0% of capital)
Ensure size_base >= min_lot and C_quote >= min_notional.

## 6) Inventory skew & skewed quoting

Track current inventory exposure in both base and quote.

exposures

V_base = base asset value (DOGE) × base_price (use mid)

V_quote = quote balance (FDUSD)

portfolio_value = V_base + V_quote

exposure = V_base / portfolio_value (fraction in DOGE)

If exposure > 0.5 + imbalance_threshold (too much DOGE), skew quotes to favor selling:

Increase ask aggressiveness (narrow ask by skew_pct) and widen bid:

ask_price_adj = ask_price * (1 - skew_pct)
bid_price_adj = bid_price * (1 + skew_pct)


If exposure < 0.5 - imbalance_threshold (too little DOGE), skew toward buying:

bid_price_adj = bid_price * (1 - skew_pct)
ask_price_adj = ask_price * (1 + skew_pct)


skew_pct can be proportional to deviation:

skew_pct = k_skew * (exposure - 0.5)


This encourages rebalancing by making the side you want to reduce more attractive.

## 7) Practical exchange mechanics (tick/lot/notional rounding)

Get exchange filters for the pair:

tick_size = min price increment

lot_size = min quantity increment

min_notional = minimum USD (FDUSD) order value

Rounding rules:

For buy prices, round down to avoid immediate crossing:

price_buy_rounded = floor(price / tick) * tick


For sell prices, round up:

price_sell_rounded = ceil(price / tick) * tick


Quantize size:

qty_rounded = floor(qty / lot_size) * lot_size


After rounding, ensure qty_rounded * price_rounded >= min_notional. If not, increase size (if funds allow) or skip order.

## 8) Fill model & expected profit calculation (per-trade economics)
expected gross capture (approx)

If you place quotes at bid and ask symmetrically, expected profit per round-trip ≈ spread captured minus adverse selection & slippage.

Define:

spread_capture ≈ ask_fill_price / bid_fill_price - 1 ≈ s_actual (approx)

E_adverse = expected adverse selection (probability price moves against you before you exit)

F_maker = maker fee (here 0)

F_taker = taker fee (for emergency hedges)

Net expected profit per completed round-trip:

Profit_per_round ≈ s_actual - E_adverse - slippage - costs


Aim to keep Profit_per_round > 0 with statistical confidence.

For a micro target / spread s and maker fee 0:

If s=0.001 (0.1%) and E_adverse + slippage ≈ 0.0003, net ≈ 0.0007.

Always track realized spreads and fill-rates to update E_adverse.

## 9) Spike detection & emergency hedging (defensive taker)
spike detection

Compute short-window return:

return_T = (mid_t - mid_{t - T}) / mid_{t - T}


If |return_T| >= spike_pct within T = T_spike_window (e.g., 1–5 minutes), declare spike. Also check orderbook depth depletion: sudden fall in cumulative bids or asks within a tight price window is additional evidence.

emergency hedge rules

If |return_T| >= spike_pct and inventory exposure > imbalance_min:

Immediately market sell or buy enough to reach target neutral exposure (e.g., exposure_target = 0.5). This will pay taker fee but prevents larger drawdown.

Log hedge cost explicitly:

hedge_cost = (market_price - weighted_entry_price) * base_amount + taker_fee


Keep allow_taker_on_emergency_exit configurable.

## 10) Order lifecycle & state machine (textual flow)

Observe: read top-of-book, compute mid_smoothed, compute volatility σ_t, compute t_refresh and s_current.

Decide: check if quoting allowed: not in cooldown, not in kill-switch, not exceeding daily loss.

Construct quotes: compute L1 & L2 bid/ask prices and sizes, apply maker-safety & skewing, round to ticks/lot.

Place: send LIMIT_MAKER or post-only limit orders for all levels. Track order ids & timestamps.

Monitor:

If an order fills, immediately place corresponding opposing level(s) as needed (if long filled at bid, place an ask TP; if short filled via sell, place bid TP).

If the order is stale > entry_timeout (configurable), cancel and respect cooldown.

Risk checks each tick:

Spike detection → emergency hedge (market).

Max position age → exit.

Stop-loss on executed unmatched position → exit (market by default).

Record all fills/fails/cancels, update metrics and exposure.

Kill-switch: if daily loss > max_daily_loss, stop trading and notify.

## 11) Event & metric logging (what to record)

Each order: {id, side, price, qty, level, timestamp, rounded_price, rounded_qty}

Each fill: {order_id, side, price, qty, fill_time, fill_type (maker/taker)}

P&L logging: realized_pnl, unrealized_pnl, fees_paid_by_type (maker/taker)

KPIs (compute daily/hourly):

fill_rate = fills / orders_placed

avg_trade_pnl = realized_pnl / completed_round_trips

realized_spread = avg( (sell_px / buy_px) - 1 )

adverse_fill_rate: proportion of fills that later lost money within X minutes

hedge_count, hedge_cost_total

Alerts:

Emergency hedge executed

API disconnects, order rejections

Daily loss hit / kill-switch triggered

Too many cancels or low fill rate

Store history in a time-series DB (or CSV logs) for analysis.

## 12) Backtesting & simulation guidance (critical)

Do not deploy live without realistic backtest + paper trading.

Data requirements

Full orderbook (level 2) tick data ideally, including updates, not just candles. If only candles, results will be overly optimistic.

Trade-by-trade ticks and orderbook snapshots if possible for microstructure realism.

Fill modeling

Simulate queue position and arrival of opposite market orders:

If your quote sits at top-of-book, fill probability depends on subsequent market pressure. Use historical orderbook events to simulate true fills.

Model partial fills and multi-level fills.

Include latency in replay:

local processing latency t_proc and network round-trip t_net affect queue priority.

Commission & slippage

Use actual maker/taker fees (maker=0 for FDUSD if confirmed for your account), apply taker fees on hedges or stop losses.

Model slippage when executing market hedges using available depth.

Backtest metrics

Realized PnL, max drawdown, Sharpe-like metric (PnL/volatility), fill rate by level, trades per day, hedges per day, win-rate, average realized spread.

Evaluate sensitivity across parameter sweeps: s_base, m_maker, t_refresh, spike_pct, skew_pct, L1/L2 sizes.

Walk-forward

Use rolling windows (train on period A, validate on period B) to detect parameter overfitting.

Stress-test on volatile periods (news, flash crashes).

## 13) Deployment checklist & operational guidelines

API keys: Create API key with spot trading enabled; restrict IPs where possible. Do not enable withdrawal on live keys used by bots.

Rate limits & reconnects: Implement robust retry/backoff for API limits and websockets reconnection. Log disconnects.

Time synchronization: Ensure server clock is NTP-synced; Binance expects timestamps for signed requests.

Minimums: Programmatically read tick_size, lot_size, min_notional. Validate before placing.

Paper trade stage: Paper trade for at least 1–2 weeks with equivalent simulated latency.

Gradual ramp: Start with 1–5% of intended capital and scale up after deterministic success metrics.

Monitoring: Real-time dashboard (+alerts via SMS/Telegram/Email) for:

Unexpected skew > 50%

Emergency hedge executions

Daily loss > 0.5% (early alert)

API errors or high cancel rates

Automatic kill switch: Stop trading on critical alerts (daily loss, no connectivity, too many order failures).

Accounting & P&L reconciliation: Daily reconciliation of exchange balances vs internal ledger; log fee savings from maker fee promo.

Backups & recovery: Save state (open orders, exposure) for fast restart. On restart, reload state and re-construct quotes conservatively (wider spreads for a short cooldown).

## 14) Parameter tuning & validation process

Define objective: e.g., maximize realized_pnl_per_day subject to max_drawdown ≤ X and hedge_cost_rate ≤ Y.

Grid search over:

s_base ∈ [0.0007, 0.0020]

m_maker ∈ [0.0001, 0.001]

t_refresh base ∈ [1s, 10s] with dynamic mapping

skew_pct ∈ [0.001, 0.02]

Backtest with realistic fill model for each parameter set.

Walk-forward validation for stability over time windows.

Choose conservative parameters that are robust across windows (not just peak performers).

Paper trade selected sets for a few weeks, capture metrics, then deploy live with low capital.

## 15) Example parameter set (conservative starter)
s_base = 0.0010 (0.10%)
s_min = 0.0008
s_max = 0.0030
m_maker = 0.0002 (2 bps)
L1 size = 0.5% of portfolio each side
L2 size = 1.0% of portfolio each side
t_min = 1s, t_max = 10s, dynamic
σ_ref = historic mean vol (computed over 30m)
k_vol = 0.05
spike_pct = 0.01 (1% in 1–3 minutes)
imbalance_max = 0.30
skew_k = 0.08
allow_taker_on_emergency_exit = True
max_daily_loss = 0.02 (2%)

## 16) Pseudocode (high-level) — implementation blueprint

Initialize connectors and read exchange filters.

Initialize state: mid_smoothed, EWMA vol, exposure, counters.

Loop every t_refresh:

Fetch top-of-book; update mid_raw, mid_smoothed.

Update EWMA volatility σ_t; compute t_refresh & s_current.

If kill-switch not active:

Compute L1/L2 prices & sizes, apply skew and maker-safety.

Round prices and sizes to tick/lot.

Place post-only LIMIT orders for each active level, or cancel/repost if different.

Monitor fills:

On fill: record, update exposure, place TP for filled side (or leave market-making to capture spread).

Risk checks:

If spike detected & exposure imbalanced → market hedge to neutral.

If stop-loss or max_age triggered → market hedge.

Update metrics & logs.

On critical alerts, disable quoting and run safe exit logic.

## 17) Monitoring dashboard & alerts (must-haves)

Live PnL (realized & unrealized)

Exposure breakdown (DOGE vs FDUSD)

Open orders table (level, price, age)

Fill rate per level, average realized spread

Hedging events (count & cost)

API health & latencies

Alerts to Telegram/email for:

Emergency hedge

Daily loss hit

Reconnect failures > X

Orders rejected for reason

## 18) Common failure modes & mitigations

Maker fee changes or promo removal

Mitigation: verify fee schedule before trading; stop if maker fee != assumed 0.

Excessive adverse selection (trend market)

Mitigation: volatility filter to widen spreads or pause; use skew to reduce exposure.

Stale orders due to latency

Mitigation: dynamic refresh (faster in high vol), local orderbook caching, aggressive kill-switch.

Wrong rounding leading to taker orders

Mitigation: conservative price rounding (buy rounded down, sell up), use post-only or LIMIT_MAKER where available.

Unexpected large market moves

Mitigation: emergency hedge (taker) + daily loss cap + manual intervention alert.

## 19) Rollout plan (safe deployment)

Unit test quoting logic, rounding, and exposure calculation locally.

Backtest with L2 data and fill model.

Paper trade with real-time market feed for 2–4 weeks.

Deploy small live (1–5% of target capital) for 2 weeks; monitor.

Gradually increase capital as metrics remain stable.

## 20) Final checklist before live

 Exchange fee & maker promo verified for your account and pair.

 Tick/lot/min_notional logic implemented and tested.

 Fill model validated in backtest & paper trade.

 Alerts & dashboard active.

 Kill-switches & daily loss limits configured.

 Logging of hedges & taker fees enabled.

 Gradual ramp plan agreed.

# Appendix — useful formulas and reference snippets
mid, spreads & quotes
mid = (best_bid + best_ask) / 2
s_current = clamp(s_base * (1 + c_vol*(σ_t/σ_ref)), s_min, s_max)
bid_L1_raw = mid * (1 - s_current/2)
ask_L1_raw = mid * (1 + s_current/2)
bid_price = min(bid_L1_raw, best_bid*(1 - m_maker))
ask_price = max(ask_L1_raw, best_ask*(1 + m_maker))

EWMA volatility
EWMA_x_t = λ * x_t + (1 - λ) * EWMA_x_{t-1}
σ_t = sqrt( EWMA(r^2) - (EWMA(r))^2 )

dynamic refresh
t_refresh = clamp(k_vol / σ_t + t_min_offset, t_min, t_max)

exposure & skew
exposure = (base_amount * mid) / portfolio_value
skew_pct = k_skew * (exposure - 0.5)  # signed
bid_adj = bid * (1 + sign*skew_pct)   # if exposure high, sign=+1 -> widen bids
ask_adj = ask * (1 - sign*skew_pct)

Closing notes (practical mindset)

This strategy is about statistical edge + operational excellence. The edge (small spread capture) is thin — success depends more on tight implementation (low latency, correct rounding, robust reconnects, realistic fill modeling) and risk controls than on fancy indicators.

Always paper trade and monitor early live results for unexpected market microstructure behaviors specific to DOGE-FDUSD.

Log everything. The smallest misconfiguration (tick rounding, feed delay) shows up in P&L.