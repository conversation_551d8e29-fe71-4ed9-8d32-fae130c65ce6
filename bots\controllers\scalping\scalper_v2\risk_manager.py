"""Production-grade risk management with circuit breakers."""
import time
from decimal import Decimal
from typing import Optional, Dict, Any
from .config import ScalperV2ControllerConfig, RiskConfig
from .types import Position, RiskMetrics, OrderState, SignalType

class RiskManager:
    """Comprehensive risk management with multiple safety layers."""
    
    def __init__(self, config: ScalperV2ControllerConfig):
        self.config = config
        self.risk_config = config.risk
        self._start_time = time.time()
        self._daily_reset_time = 0.0  # Track daily reset
        
    def can_place_order(self, 
                       signal: SignalType,
                       position: Position, 
                       risk_metrics: RiskMetrics,
                       active_orders: Dict[str, OrderState],
                       proposed_amount: Decimal,
                       current_balance: Optional[Decimal] = None) -> tuple[bool, Optional[str]]:
        """Comprehensive pre-trade risk check."""
        
        # Emergency mode check
        if risk_metrics.is_emergency_mode:
            return False, "Strategy in emergency mode"
            
        # Cooldown check
        if time.time() < risk_metrics.cooldown_until:
            return False, f"In cooldown period for {risk_metrics.cooldown_until - time.time():.1f}s"
            
        # Daily loss limit
        if (self.risk_config.max_daily_loss and 
            risk_metrics.daily_pnl <= -abs(self.risk_config.max_daily_loss)):
            return False, "Daily loss limit exceeded"
            
        # Maximum drawdown check
        if risk_metrics.max_drawdown >= self.risk_config.max_drawdown_pct:
            return False, f"Maximum drawdown exceeded: {risk_metrics.max_drawdown:.2%}"
            
        # Position size limits
        new_base_position = position.base_amount
        if signal == SignalType.BUY:
            new_base_position += proposed_amount
        elif signal == SignalType.SELL:
            new_base_position -= proposed_amount
            
        if abs(new_base_position) > self.risk_config.max_position_base:
            return False, f"Position limit exceeded: {abs(new_base_position)} > {self.risk_config.max_position_base}"
            
        # Active order limit
        if len([o for o in active_orders.values() if o.is_active]) >= self.risk_config.max_open_orders:
            return False, f"Too many open orders: {len(active_orders)} >= {self.risk_config.max_open_orders}"
            
        # Balance-based position sizing
        if current_balance:
            max_position_value = current_balance * self.risk_config.position_size_pct
            if proposed_amount * position.avg_entry_price > max_position_value:
                return False, f"Position size exceeds {self.risk_config.position_size_pct:.1%} of balance"
        
        return True, None
        
    def should_emergency_exit(self, position: Position, risk_metrics: RiskMetrics) -> tuple[bool, str]:
        """Determine if emergency exit is needed."""
        
        # Immediate exit conditions
        reasons = []
        
        if (self.risk_config.max_daily_loss and 
            risk_metrics.daily_pnl <= -abs(self.risk_config.max_daily_loss) * Decimal("1.5")):
            reasons.append("Critical daily loss")
            
        if risk_metrics.consecutive_losses >= 5:
            reasons.append("Excessive consecutive losses")
            
        if risk_metrics.max_drawdown >= self.risk_config.max_drawdown_pct * Decimal("1.2"):
            reasons.append("Critical drawdown")
            
        if len(reasons) > 0:
            return True, "; ".join(reasons)
            
        return False, ""
        
    def update_risk_metrics(self, 
                           metrics: RiskMetrics, 
                           trade_pnl: Optional[Decimal] = None,
                           trade_volume: Optional[Decimal] = None,
                           fees: Optional[Decimal] = None) -> RiskMetrics:
        """Update risk metrics after trade or periodically."""
        
        now = time.time()
        
        # Reset daily metrics if new day
        if self._should_reset_daily(now):
            metrics.daily_pnl = Decimal("0")
            metrics.daily_volume = Decimal("0")
            metrics.consecutive_losses = 0
            
        # Update trade-specific metrics
        if trade_pnl is not None:
            metrics.daily_pnl += trade_pnl
            metrics.realized_pnl += trade_pnl
            
            if trade_pnl < 0:
                metrics.consecutive_losses += 1
                # Apply cooldown after loss
                if metrics.consecutive_losses >= 3:
                    metrics.cooldown_until = now + self.risk_config.cooldown_after_loss_s
            else:
                metrics.consecutive_losses = 0
                
        if trade_volume:
            metrics.daily_volume += trade_volume
            
        if fees:
            metrics.total_fees_paid += fees
            
        # Update max drawdown
        if trade_pnl and trade_pnl < 0:
            current_dd = abs(trade_pnl) / max(metrics.daily_volume, Decimal("1"))
            metrics.max_drawdown = max(metrics.max_drawdown, current_dd)
            
        metrics.last_trade_time = now
        return metrics
        
    def _should_reset_daily(self, current_time: float) -> bool:
        """Check if daily metrics should reset."""
        current_day = int(current_time // 86400)  # Days since epoch
        last_day = int(self._daily_reset_time // 86400)
        
        if current_day > last_day:
            self._daily_reset_time = current_time
            return True
        return False
