"""Production example for running the V2 scalping strategy."""
import asyncio
import logging
import signal
from decimal import Decimal

from hummingbot.client.hummingbot_application import HummingbotApplication
from ..config import ScalperV2ControllerConfig, RiskConfig
from ..controller import ScalperV2Controller

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scalping_strategy.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class StrategyRunner:
    """Production strategy runner with proper lifecycle management."""
    
    def __init__(self):
        self.strategy = None
        self.app = None
        self._shutdown_event = asyncio.Event()
        
    async def setup(self):
        """Initialize Hummingbot application and strategy."""
        try:
            # Initialize Hummingbot
            self.app = HummingbotApplication.main_application()
            await self.app.start()
            
            # Create strategy configuration
            risk_config = RiskConfig(
                max_position_base=Decimal("0.1"),
                max_position_quote=Decimal("1000"),
                max_daily_loss=Decimal("50"),
                max_drawdown_pct=Decimal("0.03"),
                position_size_pct=Decimal("0.02"),
                max_open_orders=5,
                cooldown_after_loss_s=30.0,
                emergency_exit_spread=Decimal("0.002")
            )
            
            config = ScalperV2ControllerConfig(
                exchange="binance",
                trading_pair="BTC-USDT",
                order_amount=Decimal("0.001"),
                max_trade_notional=Decimal("100"),
                tick_interval=0.5,
                order_refresh_time=3.0,
                tp_spread=Decimal("0.0015"),
                sl_spread=Decimal("0.001"),
                maker_fee=Decimal("0.0002"),
                taker_fee=Decimal("0.0007"),
                ob_levels=5,
                trade_window_s=2.0,
                min_imbalance_ratio=Decimal("1.5"),
                min_flow_ratio=Decimal("1.2"),
                risk=risk_config,
                state_save_interval=5.0
            )
            
            # Create and start strategy
            self.strategy = ScalperV2Controller(config)
            await self.strategy.start()
            
            logger.info("Strategy initialized successfully")
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            raise
            
    async def run(self):
        """Main run loop."""
        try:
            logger.info("Starting strategy execution...")
            await self._shutdown_event.wait()  # Wait for shutdown signal
            
        except Exception as e:
            logger.error(f"Runtime error: {e}")
        finally:
            await self.cleanup()
            
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self.strategy:
                await self.strategy.stop()
                
            if self.app:
                await self.app.stop()
                
            logger.info("Cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
            
    def shutdown(self):
        """Trigger graceful shutdown."""
        logger.info("Shutdown requested...")
        self._shutdown_event.set()

async def main():
    """Main entry point."""
    runner = StrategyRunner()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        runner.shutdown()
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await runner.setup()
        await runner.run()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
