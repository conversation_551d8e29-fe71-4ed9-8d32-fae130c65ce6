"""Enhanced async persistence with state management and recovery."""
import asyncio
import asyncpg
import json
from typing import Any, Dict, Optional, List
from decimal import Decimal
from .types import OrderState, Position, RiskMetrics, OrderStatus

# Enhanced DDL with state management
DDL_ORDERS = """
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    client_order_id TEXT UNIQUE NOT NULL,
    exchange_order_id TEXT,
    trading_pair TEXT NOT NULL,
    side TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    price NUMERIC NOT NULL,
    filled_amount NUMERIC NOT NULL DEFAULT 0,
    status TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    fees_paid NUMERIC NOT NULL DEFAULT 0,
    avg_fill_price NUMERIC
);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_exchange_id ON orders(exchange_order_id);
"""

DDL_POSITIONS = """
CREATE TABLE IF NOT EXISTS positions (
    id SERIAL PRIMARY KEY,
    trading_pair TEXT NOT NULL,
    base_amount NUMERIC NOT NULL DEFAULT 0,
    quote_amount NUMERIC NOT NULL DEFAULT 0,
    unrealized_pnl NUMERIC NOT NULL DEFAULT 0,
    realized_pnl NUMERIC NOT NULL DEFAULT 0,
    avg_entry_price NUMERIC,
    last_update TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_positions_pair ON positions(trading_pair);
"""

DDL_RISK_STATE = """
CREATE TABLE IF NOT EXISTS risk_state (
    id SERIAL PRIMARY KEY,
    trading_pair TEXT NOT NULL,
    daily_pnl NUMERIC NOT NULL DEFAULT 0,
    daily_volume NUMERIC NOT NULL DEFAULT 0,
    max_position_reached NUMERIC NOT NULL DEFAULT 0,
    total_fees_paid NUMERIC NOT NULL DEFAULT 0,
    win_rate NUMERIC NOT NULL DEFAULT 0,
    profit_factor NUMERIC NOT NULL DEFAULT 1,
    max_drawdown NUMERIC NOT NULL DEFAULT 0,
    consecutive_losses INTEGER NOT NULL DEFAULT 0,
    last_trade_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_emergency_mode BOOLEAN NOT NULL DEFAULT FALSE,
    cooldown_until TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_date DATE NOT NULL DEFAULT CURRENT_DATE
);
CREATE INDEX IF NOT EXISTS idx_risk_pair_date ON risk_state(trading_pair, created_date);
"""

DDL_FILLS = """
CREATE TABLE IF NOT EXISTS fills (
    id SERIAL PRIMARY KEY,
    client_order_id TEXT NOT NULL,
    exchange_order_id TEXT,
    amount NUMERIC NOT NULL,
    price NUMERIC NOT NULL,
    fee NUMERIC NOT NULL DEFAULT 0,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    trading_pair TEXT NOT NULL
);
"""

class StateManager:
    """Production-grade state management with recovery capabilities."""
    
    def __init__(self, dsn: str, trading_pair: str):
        self._dsn = dsn
        self._trading_pair = trading_pair
        self._pool: Optional[asyncpg.pool.Pool] = None
        self._queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self._worker_task: Optional[asyncio.Task] = None
        self._running = False
        
    async def start(self):
        """Initialize database and start background worker."""
        self._pool = await asyncpg.create_pool(
            dsn=self._dsn, 
            min_size=2, 
            max_size=10,
            command_timeout=30
        )
        
        # Create tables
        async with self._pool.acquire() as conn:
            await conn.execute(DDL_ORDERS)
            await conn.execute(DDL_POSITIONS)
            await conn.execute(DDL_RISK_STATE)
            await conn.execute(DDL_FILLS)
            
        self._running = True
        self._worker_task = asyncio.create_task(self._persistence_worker())
        
    async def stop(self):
        """Graceful shutdown with queue flush."""
        self._running = False
        
        # Process remaining items
        while not self._queue.empty():
            try:
                await self._process_queue_item()
            except Exception as e:
                print(f"Error processing final queue items: {e}")
                
        if self._worker_task:
            await self._worker_task
            
        if self._pool:
            await self._pool.close()
            
    async def _persistence_worker(self):
        """Background worker with error handling and backpressure."""
        while self._running:
            try:
                await self._process_queue_item()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"Persistence worker error: {e}")
                await asyncio.sleep(1)  # Back off on errors
                
    async def _process_queue_item(self):
        """Process single queue item with timeout."""
        try:
            coro_func = await asyncio.wait_for(self._queue.get(), timeout=1.0)
            await coro_func()
        except asyncio.TimeoutError:
            pass  # No items in queue
            
    async def _enqueue(self, coro_func):
        """Enqueue with backpressure handling."""
        try:
            self._queue.put_nowait(coro_func)
        except asyncio.QueueFull:
            # Drop oldest item if queue is full (backpressure)
            try:
                self._queue.get_nowait()
                self._queue.put_nowait(coro_func)
            except asyncio.QueueEmpty:
                pass
    
    # --- Recovery Methods ---
    async def recover_state(self) -> tuple[Dict[str, OrderState], Position, RiskMetrics]:
        """Recover complete strategy state after restart."""
        orders = await self.get_active_orders()
        position = await self.get_current_position()
        risk_metrics = await self.get_risk_metrics()
        return orders, position, risk_metrics
        
    async def get_active_orders(self) -> Dict[str, OrderState]:
        """Get all active orders for recovery."""
        async with self._pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT client_order_id, exchange_order_id, side, amount, price, 
                       filled_amount, status, created_at, updated_at, fees_paid, avg_fill_price
                FROM orders 
                WHERE trading_pair = $1 AND status IN ('open', 'partially_filled')
                ORDER BY created_at DESC
            """, self._trading_pair)
            
            orders = {}
            for row in rows:
                order = OrderState(
                    client_order_id=row['client_order_id'],
                    exchange_order_id=row['exchange_order_id'],
                    side=row['side'],
                    amount=Decimal(str(row['amount'])),
                    price=Decimal(str(row['price'])),
                    filled_amount=Decimal(str(row['filled_amount'])),
                    status=OrderStatus(row['status']),
                    created_at=row['created_at'].timestamp(),
                    updated_at=row['updated_at'].timestamp(),
                    fees_paid=Decimal(str(row['fees_paid'])),
                    avg_fill_price=Decimal(str(row['avg_fill_price'])) if row['avg_fill_price'] else None
                )
                orders[order.client_order_id] = order
            return orders
            
    async def get_current_position(self) -> Position:
        """Get current position for recovery."""
        async with self._pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT base_amount, quote_amount, unrealized_pnl, realized_pnl, 
                       avg_entry_price, last_update
                FROM positions 
                WHERE trading_pair = $1 
                ORDER BY last_update DESC LIMIT 1
            """, self._trading_pair)
            
            if not row:
                return Position()
                
            return Position(
                base_amount=Decimal(str(row['base_amount'])),
                quote_amount=Decimal(str(row['quote_amount'])),
                unrealized_pnl=Decimal(str(row['unrealized_pnl'])),
                realized_pnl=Decimal(str(row['realized_pnl'])),
                avg_entry_price=Decimal(str(row['avg_entry_price'])) if row['avg_entry_price'] else None,
                last_update=row['last_update'].timestamp()
            )
            
    async def get_risk_metrics(self) -> RiskMetrics:
        """Get current risk metrics for recovery."""
        async with self._pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT daily_pnl, daily_volume, max_position_reached, total_fees_paid,
                       win_rate, profit_factor, max_drawdown, consecutive_losses,
                       last_trade_time, is_emergency_mode, cooldown_until
                FROM risk_state 
                WHERE trading_pair = $1 AND created_date = CURRENT_DATE
                ORDER BY id DESC LIMIT 1
            """, self._trading_pair)
            
            if not row:
                return RiskMetrics()
                
            return RiskMetrics(
                daily_pnl=Decimal(str(row['daily_pnl'])),
                daily_volume=Decimal(str(row['daily_volume'])),
                max_position_reached=Decimal(str(row['max_position_reached'])),
                total_fees_paid=Decimal(str(row['total_fees_paid'])),
                win_rate=float(row['win_rate']),
                profit_factor=float(row['profit_factor']),
                max_drawdown=Decimal(str(row['max_drawdown'])),
                consecutive_losses=int(row['consecutive_losses']),
                last_trade_time=row['last_trade_time'].timestamp(),
                is_emergency_mode=bool(row['is_emergency_mode']),
                cooldown_until=row['cooldown_until'].timestamp()
            )
    
    # --- Persistence Methods ---
    async def save_order(self, order: OrderState):
        """Persist order state asynchronously."""
        async def _save():
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO orders (client_order_id, exchange_order_id, trading_pair, side, 
                                      amount, price, filled_amount, status, created_at, updated_at, 
                                      fees_paid, avg_fill_price)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, to_timestamp($9), to_timestamp($10), $11, $12)
                    ON CONFLICT (client_order_id) DO UPDATE SET
                        exchange_order_id = EXCLUDED.exchange_order_id,
                        filled_amount = EXCLUDED.filled_amount,
                        status = EXCLUDED.status,
                        updated_at = EXCLUDED.updated_at,
                        fees_paid = EXCLUDED.fees_paid,
                        avg_fill_price = EXCLUDED.avg_fill_price
                """, order.client_order_id, order.exchange_order_id, self._trading_pair,
                order.side, str(order.amount), str(order.price), str(order.filled_amount),
                order.status.value, order.created_at, order.updated_at, 
                str(order.fees_paid), str(order.avg_fill_price) if order.avg_fill_price else None)
        await self._enqueue(_save)
        
    async def save_position(self, position: Position):
        """Persist position state."""
        async def _save():
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO positions (trading_pair, base_amount, quote_amount, 
                                         unrealized_pnl, realized_pnl, avg_entry_price, last_update)
                    VALUES ($1, $2, $3, $4, $5, $6, to_timestamp($7))
                """, self._trading_pair, str(position.base_amount), str(position.quote_amount),
                str(position.unrealized_pnl), str(position.realized_pnl),
                str(position.avg_entry_price) if position.avg_entry_price else None,
                position.last_update)
        await self._enqueue(_save)
        
    async def save_risk_metrics(self, metrics: RiskMetrics):
        """Persist risk metrics."""
        async def _save():
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO risk_state (trading_pair, daily_pnl, daily_volume, 
                                          max_position_reached, total_fees_paid, win_rate,
                                          profit_factor, max_drawdown, consecutive_losses,
                                          last_trade_time, is_emergency_mode, cooldown_until)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, to_timestamp($10), $11, to_timestamp($12))
                    ON CONFLICT (trading_pair, created_date) DO UPDATE SET
                        daily_pnl = EXCLUDED.daily_pnl,
                        daily_volume = EXCLUDED.daily_volume,
                        max_position_reached = EXCLUDED.max_position_reached,
                        total_fees_paid = EXCLUDED.total_fees_paid,
                        win_rate = EXCLUDED.win_rate,
                        profit_factor = EXCLUDED.profit_factor,
                        max_drawdown = EXCLUDED.max_drawdown,
                        consecutive_losses = EXCLUDED.consecutive_losses,
                        last_trade_time = EXCLUDED.last_trade_time,
                        is_emergency_mode = EXCLUDED.is_emergency_mode,
                        cooldown_until = EXCLUDED.cooldown_until
                """, self._trading_pair, str(metrics.daily_pnl), str(metrics.daily_volume),
                str(metrics.max_position_reached), str(metrics.total_fees_paid), metrics.win_rate,
                metrics.profit_factor, str(metrics.max_drawdown), metrics.consecutive_losses,
                metrics.last_trade_time, metrics.is_emergency_mode, metrics.cooldown_until)
        await self._enqueue(_save)
