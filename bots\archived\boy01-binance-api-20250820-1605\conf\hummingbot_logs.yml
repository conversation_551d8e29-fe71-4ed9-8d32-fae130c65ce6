---
version: 1
template_version: 12

formatters:
    simple:
        format: "%(asctime)s - %(process)d - %(name)s - %(levelname)s - %(message)s"

handlers:
    console:
        class: hummingbot.logger.cli_handler.CLIHandler
        level: DEBUG
        formatter: simple
        stream: ext://sys.stdout
    console_warning:
        class: hummingbot.logger.cli_handler.CLIHandler
        level: WARNING
        formatter: simple
        stream: ext://sys.stdout
    console_info:
        class: hummingbot.logger.cli_handler.CLIHandler
        level: INFO
        formatter: simple
        stream: ext://sys.stdout
    file_handler:
        class: logging.handlers.TimedRotatingFileHandler
        level: DEBUG
        formatter: simple
        filename: $PROJECT_DIR/logs/logs_$STRATEGY_FILE_PATH.log
        encoding: utf8
        when: "D"
        interval: 1
        backupCount: 7
    "null":
        class: logging.NullHandler
        level: DEBUG

loggers:
    hummingbot.core.utils.eth_gas_station_lookup:
        level: NETWORK
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.logger.log_server_client:
        level: WARNING
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.logger.reporting_proxy_handler:
        level: WARNING
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.strategy:
        level: NETWORK
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.connector:
        level: NETWORK
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.client:
        level: NETWORK
        propagate: false
        handlers: [console, file_handler]
        mqtt: true
    hummingbot.core.event.event_reporter:
        level: EVENT_LOG
        propagate: false
        handlers: [file_handler]
        mqtt: false
    conf:
        level: NETWORK
        handlers: ["null"]
        propagate: false
        mqtt: false

root:
    level: INFO
    handlers: [console, file_handler]
    mqtt: true
