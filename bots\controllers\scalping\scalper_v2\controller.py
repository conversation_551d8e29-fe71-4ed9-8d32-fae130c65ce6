"""Main V2 strategy implementation with comprehensive state management."""
import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict, Any, Optional, List

from hummingbot.strategy.strategy_v2_base import StrategyV2Base
from hummingbot.strategy_v2.controllers.controller_base import ControllerBase, ControllerConfigBase
from hummingbot.core.data_type.common import OrderType
from hummingbot.core.utils.fixed_rate_source import FixedRateSource

from .config import ScalperV2ControllerConfig
from .types import Position, RiskMetrics, SignalType
from .persistence import StateManager
from .risk_manager import RiskManager  
from .signals import EnhancedSignalGenerator
from .executor import EnhancedExecutor
from .utils import split_trading_pair
from .config import ScalperV2ControllerConfig

logger = logging.getLogger(__name__)

class ScalperV2Controller(StrategyV2Base):
    """Production-grade V2 scalping strategy with comprehensive risk management."""

    # ✅ --- THIS IS THE CRITICAL FIX --- ✅
    # Link the configuration class to the strategy class
    CONFIG_CLASS = ScalperV2ControllerConfig
    # ✅ --------------------------------- ✅
    
    def __init__(self, config: ScalperV2ControllerConfig):
        super().__init__()
        
        self.config = config
        self.trading_pair = config.trading_pair
        self.exchange_name = config.exchange
        
        # Core components
        self.state_manager = StateManager(config.db_dsn, config.trading_pair)
        self.risk_manager = RiskManager(config)
        self.signal_generator = EnhancedSignalGenerator(config)
        self.executor = EnhancedExecutor(self, config, self.state_manager)
        
        # State variables
        self._position = Position()
        self._risk_metrics = RiskMetrics()
        self._active_orders: Dict[str, Any] = {}
        self._last_tick_time = 0.0
        self._last_state_save = 0.0
        
        # Status flags
        self._is_initialized = False
        self._is_emergency_mode = False
        
        # Performance tracking
        self._tick_count = 0
        self._total_tick_time = 0.0
        
    async def start(self):
        """Initialize strategy and recover state."""
        try:
            logger.info("Starting ScalperV2Controller...")
            
            # Initialize state manager
            await self.state_manager.start()
            
            # Recover state from database
            recovered_orders, recovered_position, recovered_metrics = await self.state_manager.recover_state()
            
            self._position = recovered_position
            self._risk_metrics = recovered_metrics
            
            # Initialize executor with recovered state
            await self.executor.initialize_from_state(recovered_orders)
            
            # Start main strategy loop
            self.start_clock()
            
            self._is_initialized = True
            logger.info(f"Strategy initialized successfully. Position: {self._position.side.value}, "
                       f"Active orders: {len(recovered_orders)}")
                       
        except Exception as e:
            logger.error(f"Failed to start strategy: {e}")
            raise
            
    async def stop(self):
        """Graceful strategy shutdown."""
        try:
            logger.info("Stopping ScalperV2Controller...")
            
            # Cancel all active orders
            if self.executor:
                await self.executor.cancel_all_orders()
                
            # Save final state
            await self._save_current_state()
            
            # Stop state manager
            if self.state_manager:
                await self.state_manager.stop()
                
            # Stop clock
            self.stop_clock()
            
            logger.info("Strategy stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during strategy shutdown: {e}")
            
    def tick(self):
        """Main strategy execution loop."""
        if not self._is_initialized:
            return
            
        start_time = time.time()
        
        try:
            asyncio.create_task(self._async_tick())
            
        except Exception as e:
            logger.error(f"Error in strategy tick: {e}")
            
        finally:
            # Performance tracking
            tick_duration = time.time() - start_time
            self._tick_count += 1
            self._total_tick_time += tick_duration
            
            if self._tick_count % 100 == 0:
                avg_tick_time = self._total_tick_time / self._tick_count
                logger.info(f"Performance: {avg_tick_time*1000:.2f}ms avg tick time over {self._tick_count} ticks")
                
    async def _async_tick(self):
        """Async strategy logic execution."""
        now = time.time()
        
        # Rate limiting
        if now - self._last_tick_time < self.config.tick_interval:
            return
            
        self._last_tick_time = now
        
        # Update position and risk metrics
        await self._update_position()
        self._update_risk_metrics()
        
        # Emergency checks
        if await self._check_emergency_conditions():
            return
            
        # Generate trading signal
        signal, confidence = await self._generate_signal()
        
        # Execute trading logic
        if signal != SignalType.HOLD:
            await self._execute_signal(signal, confidence)
            
        # Periodic state persistence
        await self._periodic_state_save(now)
        
        # Order management
        await self._manage_orders()
        
    async def _update_position(self):
        """Update current position based on recent fills."""
        try:
            # Get current balances
            base_balance = await self.get_balance(self.exchange_name, split_trading_pair(self.trading_pair)[0])
            quote_balance = await self.get_balance(self.exchange_name, split_trading_pair(self.trading_pair)[1])
            
            # Update position (simplified - in production would track fills more precisely)
            self._position.last_update = time.time()
            
            # Get current market price for P&L calculation
            ticker = await self.get_price_by_type(
                self.exchange_name, 
                self.trading_pair, 
                FixedRateSource()
            )
            
            if ticker and self._position.avg_entry_price:
                current_price = Decimal(str(ticker))
                price_diff = current_price - self._position.avg_entry_price
                self._position.unrealized_pnl = self._position.base_amount * price_diff
                
        except Exception as e:
            logger.error(f"Error updating position: {e}")
            
    def _update_risk_metrics(self):
        """Update risk metrics periodically."""
        self._risk_metrics = self.risk_manager.update_risk_metrics(self._risk_metrics)
        
    async def _check_emergency_conditions(self) -> bool:
        """Check if emergency exit is needed."""
        should_exit, reason = self.risk_manager.should_emergency_exit(self._position, self._risk_metrics)
        
        if should_exit and not self._is_emergency_mode:
            logger.critical(f"EMERGENCY EXIT TRIGGERED: {reason}")
            self._is_emergency_mode = True
            self._risk_metrics.is_emergency_mode = True
            
            # Execute emergency exit
            success = await self.executor.emergency_exit_position(self._position)
            
            if success:
                logger.info("Emergency exit executed successfully")
            else:
                logger.error("Emergency exit failed - manual intervention required")
                
            await self._save_current_state()
            return True
            
        return False
        
    async def _generate_signal(self) -> tuple[SignalType, float]:
        """Generate trading signal from market data."""
        try:
            # Get market data
            orderbook = await self.get_order_book(self.exchange_name, self.trading_pair)
            recent_trades = await self.get_recent_trades(self.exchange_name, self.trading_pair)
            
            # Calculate current spread
            if orderbook and orderbook.best_bid and orderbook.best_ask:
                current_spread = Decimal(str(orderbook.best_ask)) - Decimal(str(orderbook.best_bid))
                spread_pct = current_spread / Decimal(str(orderbook.best_ask))
                
                # Don't trade if spread is too wide
                if spread_pct > self.config.min_spread * Decimal("2"):
                    return SignalType.HOLD, 0.0
                    
                # Convert to signal generator format
                ob_data = {
                    'bids': [[float(bid.price), float(bid.amount)] for bid in orderbook.bid_entries()[:10]],
                    'asks': [[float(ask.price), float(ask.amount)] for ask in orderbook.ask_entries()[:10]]
                }
                
                trades_data = [
                    {
                        'price': float(trade.price),
                        'amount': float(trade.amount), 
                        'side': 'buy' if trade.is_buy else 'sell',
                        'timestamp': trade.timestamp
                    } for trade in recent_trades[-50:] if recent_trades
                ]
                
                return self.signal_generator.generate_signal(ob_data, trades_data, current_spread)
                
            return SignalType.HOLD, 0.0
            
        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return SignalType.HOLD, 0.0
            
    async def _execute_signal(self, signal: SignalType, confidence: float):
        """Execute trading signal if risk checks pass."""
        try:
            # Get current market data
            orderbook = await self.get_order_book(self.exchange_name, self.trading_pair)
            if not orderbook or not orderbook.best_bid or not orderbook.best_ask:
                return
                
            # Calculate order parameters
            if signal == SignalType.BUY:
                entry_price = Decimal(str(orderbook.best_bid))  # Buy at bid for maker rebate
                is_buy = True
            else:
                entry_price = Decimal(str(orderbook.best_ask))  # Sell at ask for maker rebate
                is_buy = False
                
            # Dynamic position sizing based on confidence
            base_amount = self.config.order_amount * Decimal(str(confidence))
            
            # Get current balance for position sizing
            quote_balance = await self.get_balance(self.exchange_name, split_trading_pair(self.trading_pair)[1])
            current_balance = Decimal(str(quote_balance)) if quote_balance else None
            
            # Risk management check
            can_trade, risk_reason = self.risk_manager.can_place_order(
                signal=signal,
                position=self._position,
                risk_metrics=self._risk_metrics,
                active_orders=self.executor.get_active_orders(),
                proposed_amount=base_amount,
                current_balance=current_balance
            )
            
            if not can_trade:
                logger.info(f"Trade blocked by risk manager: {risk_reason}")
                return
                
            # Place order
            order_id = await self.executor.place_order(
                is_buy=is_buy,
                amount=base_amount,
                price=entry_price,
                order_type=OrderType.LIMIT
            )
            
            if order_id:
                logger.info(f"Order placed: {signal.value} {base_amount} at {entry_price} (confidence: {confidence:.2f})")
            else:
                logger.warning(f"Failed to place {signal.value} order")
                
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            
    async def _manage_orders(self):
        """Manage existing orders - refresh stale orders."""
        try:
            current_time = time.time()
            stale_orders = []
            
            for client_id, order in self.executor.get_active_orders().items():
                # Check if order is stale
                if current_time - order.created_at > self.config.order_refresh_time:
                    stale_orders.append(client_id)
                    
            # Cancel and replace stale orders
            for client_id in stale_orders:
                success = await self.executor.cancel_order(client_id)
                if success:
                    logger.info(f"Cancelled stale order: {client_id}")
                    
        except Exception as e:
            logger.error(f"Error managing orders: {e}")
            
    async def _periodic_state_save(self, current_time: float):
        """Periodically save strategy state."""
        if current_time - self._last_state_save > self.config.state_save_interval:
            await self._save_current_state()
            self._last_state_save = current_time
            
    async def _save_current_state(self):
        """Save current strategy state to database."""
        try:
            await self.state_manager.save_position(self._position)
            await self.state_manager.save_risk_metrics(self._risk_metrics)
        except Exception as e:
            logger.error(f"Error saving state: {e}")
            
    # V2 Framework required methods
    def create_proposal_based_on_order_book(self, order_book) -> List[Any]:
        """V2 framework method - not used in this implementation."""
        return []
        
    def adjust_proposal_to_budget(self, proposal: List[Any]) -> List[Any]:
        """V2 framework method - not used in this implementation."""
        return proposal
