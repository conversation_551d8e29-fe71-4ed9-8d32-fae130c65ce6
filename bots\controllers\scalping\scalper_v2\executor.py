"""Enhanced V2 framework compliant executor with comprehensive order management."""
import asyncio
import logging
import time
import uuid
from decimal import Decimal
from typing import Dict, Optional, List, Any
from threading import RLock

from hummingbot.strategy.strategy_v2_base import StrategyV2Base
from hummingbot.core.data_type.common import OrderType, PositionAction, TradeType
from hummingbot.core.data_type.in_flight_order import InFlightOrder
from hummingbot.core.event.events import (
    OrderFilledEvent, BuyOrderCompletedEvent, SellOrderCompletedEvent, 
    OrderCancelledEvent, OrderExpiredEvent, MarketOrderFailureEvent
)

from .config import ScalperV2ControllerConfig
from .types import OrderState, OrderStatus, Position
from .utils import split_trading_pair, compute_tp_price_with_fees

logger = logging.getLogger(__name__)

class EnhancedExecutor:
    """V2 framework compliant executor with state recovery and comprehensive order management."""
    
    def __init__(self, strategy: StrategyV2Base, config: ScalperV2ControllerConfig, state_manager):
        self.strategy = strategy
        self.config = config
        self.state_manager = state_manager
        self._lock = RLock()
        self._orders: Dict[str, OrderState] = {}
        self._pending_cancellations: set = set()
        
        self.base_asset, self.quote_asset = split_trading_pair(config.trading_pair)
        
        # Register event listeners
        self._register_events()
        
    def _register_events(self):
        """Register all necessary event listeners."""
        events = [
            (OrderFilledEvent, self._on_order_filled),
            (BuyOrderCompletedEvent, self._on_order_completed),
            (SellOrderCompletedEvent, self._on_order_completed),
            (OrderCancelledEvent, self._on_order_cancelled),
            (OrderExpiredEvent, self._on_order_expired),
            (MarketOrderFailureEvent, self._on_order_failed)
        ]
        
        for event_type, handler in events:
            self.strategy.add_markets_listener(event_type, handler)
            
    async def initialize_from_state(self, recovered_orders: Dict[str, OrderState]):
        """Initialize executor state from recovered data."""
        with self._lock:
            self._orders = recovered_orders.copy()
            
        # Reconcile with exchange state
        await self._reconcile_orders()
        
    async def _reconcile_orders(self):
        """Reconcile local order state with exchange state."""
        logger.info("Reconciling order state with exchange...")
        
        try:
            # Get all active orders from exchange
            active_orders = await self.strategy.get_active_orders(
                connector_name=self.config.exchange,
                trading_pair=self.config.trading_pair
            )
            
            exchange_order_ids = {order.exchange_order_id for order in active_orders}
            
            # Cancel orders that exist locally but not on exchange
            for client_id, order in list(self._orders.items()):
                if (order.is_active and 
                    order.exchange_order_id and 
                    order.exchange_order_id not in exchange_order_ids):
                    
                    logger.warning(f"Order {client_id} not found on exchange, marking as cancelled")
                    order.status = OrderStatus.CANCELED
                    await self.state_manager.save_order(order)
                    
            # Update status of orders found on exchange
            for exchange_order in active_orders:
                matching_order = None
                for order in self._orders.values():
                    if order.exchange_order_id == exchange_order.exchange_order_id:
                        matching_order = order
                        break
                        
                if matching_order:
                    # Update with exchange data
                    matching_order.filled_amount = Decimal(str(exchange_order.executed_amount_base))
                    if exchange_order.executed_amount_base > 0:
                        matching_order.status = OrderStatus.PARTIALLY_FILLED
                    await self.state_manager.save_order(matching_order)
                    
        except Exception as e:
            logger.error(f"Error during order reconciliation: {e}")
            
    async def place_order(self, 
                         is_buy: bool, 
                         amount: Decimal, 
                         price: Decimal,
                         order_type: OrderType = OrderType.LIMIT) -> Optional[str]:
        """Place order with comprehensive validation and tracking."""
        
        try:
            # Pre-flight validation
            if not await self._validate_order(is_buy, amount, price):
                return None
                
            # Generate client order ID
            client_order_id = f"scalper_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
            
            # Create order tracking
            order_state = OrderState(
                client_order_id=client_order_id,
                side="BUY" if is_buy else "SELL",
                amount=amount,
                price=price,
                status=OrderStatus.PENDING
            )
            
            with self._lock:
                self._orders[client_order_id] = order_state
                
            # Place order through V2 framework
            order_result = await self.strategy.place_order(
                connector_name=self.config.exchange,
                trading_pair=self.config.trading_pair,
                order_type=order_type,
                trade_type=TradeType.BUY if is_buy else TradeType.SELL,
                amount=amount,
                price=price,
                client_order_id=client_order_id
            )
            
            if order_result.get('success'):
                exchange_order_id = order_result.get('exchange_order_id')
                order_state.exchange_order_id = exchange_order_id
                order_state.status = OrderStatus.OPEN
                logger.info(f"Order placed successfully: {client_order_id} -> {exchange_order_id}")
            else:
                order_state.status = OrderStatus.FAILED
                logger.error(f"Failed to place order: {order_result.get('error')}")
                
            await self.state_manager.save_order(order_state)
            return client_order_id if order_result.get('success') else None
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            if client_order_id in self._orders:
                self._orders[client_order_id].status = OrderStatus.FAILED
            return None
            
    async def cancel_order(self, client_order_id: str) -> bool:
        """Cancel order with proper state management."""
        
        with self._lock:
            order = self._orders.get(client_order_id)
            if not order or not order.is_active:
                return False
                
            if client_order_id in self._pending_cancellations:
                return False  # Already cancelling
                
            self._pending_cancellations.add(client_order_id)
            
        try:
            success = await self.strategy.cancel_order(
                connector_name=self.config.exchange,
                trading_pair=self.config.trading_pair,
                client_order_id=client_order_id
            )
            
            if success:
                order.status = OrderStatus.CANCELED
                await self.state_manager.save_order(order)
                logger.info(f"Order cancelled successfully: {client_order_id}")
            else:
                logger.warning(f"Failed to cancel order: {client_order_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error cancelling order {client_order_id}: {e}")
            return False
        finally:
            with self._lock:
                self._pending_cancellations.discard(client_order_id)
                
    async def cancel_all_orders(self) -> int:
        """Cancel all active orders."""
        cancelled_count = 0
        
        active_orders = [
            (client_id, order) for client_id, order in self._orders.items() 
            if order.is_active
        ]
        
        # Cancel orders concurrently
        cancel_tasks = [
            self.cancel_order(client_id) for client_id, _ in active_orders
        ]
        
        if cancel_tasks:
            results = await asyncio.gather(*cancel_tasks, return_exceptions=True)
            cancelled_count = sum(1 for r in results if r is True)
            
        logger.info(f"Cancelled {cancelled_count} orders")
        return cancelled_count
        
    async def emergency_exit_position(self, position: Position) -> bool:
        """Emergency market exit of current position."""
        if position.side.value == "neutral":
            return True
            
        try:
            # Cancel all existing orders first
            await self.cancel_all_orders()
            
            # Calculate exit amount
            exit_amount = abs(position.base_amount)
            if exit_amount < Decimal("0.0001"):  # Dust threshold
                return True
                
            # Determine exit side
            is_sell = position.base_amount > 0  # Sell to exit long, buy to exit short
            
            # Use market order for guaranteed execution
            client_order_id = await self.place_order(
                is_buy=not is_sell,
                amount=exit_amount,
                price=Decimal("0"),  # Market order
                order_type=OrderType.MARKET
            )
            
            if client_order_id:
                logger.warning(f"Emergency exit initiated: {client_order_id}")
                return True
            else:
                logger.error("Failed to place emergency exit order")
                return False
                
        except Exception as e:
            logger.error(f"Emergency exit failed: {e}")
            return False
            
    async def _validate_order(self, is_buy: bool, amount: Decimal, price: Decimal) -> bool:
        """Validate order parameters before placement."""
        
        # Basic validation
        if amount <= 0 or price <= 0:
            logger.error(f"Invalid order parameters: amount={amount}, price={price}")
            return False
            
        # Balance check
        try:
            if is_buy:
                required_balance = amount * price
                available_balance = await self.strategy.get_balance(
                    connector_name=self.config.exchange,
                    asset=self.quote_asset
                )
                
                if available_balance < required_balance:
                    logger.warning(f"Insufficient {self.quote_asset} balance: {available_balance} < {required_balance}")
                    return False
            else:
                available_balance = await self.strategy.get_balance(
                    connector_name=self.config.exchange, 
                    asset=self.base_asset
                )
                
                if available_balance < amount:
                    logger.warning(f"Insufficient {self.base_asset} balance: {available_balance} < {amount}")
                    return False
                    
        except Exception as e:
            logger.error(f"Balance check failed: {e}")
            return False
            
        return True
        
    # Event Handlers
    def _on_order_filled(self, event: OrderFilledEvent):
        """Handle order fill events."""
        client_order_id = getattr(event, 'client_order_id', None)
        if not client_order_id or client_order_id not in self._orders:
            return
            
        with self._lock:
            order = self._orders[client_order_id]
            fill_amount = Decimal(str(event.amount))
            fill_price = Decimal(str(event.price))
            
            order.filled_amount += fill_amount
            order.fees_paid += Decimal(str(getattr(event, 'trade_fee', 0)))
            
            # Update average fill price
            if order.avg_fill_price is None:
                order.avg_fill_price = fill_price
            else:
                total_filled_value = order.avg_fill_price * (order.filled_amount - fill_amount)
                total_filled_value += fill_price * fill_amount
                order.avg_fill_price = total_filled_value / order.filled_amount
                
            # Update status
            if order.filled_amount >= order.amount:
                order.status = OrderStatus.FILLED
            else:
                order.status = OrderStatus.PARTIALLY_FILLED
                
            order.updated_at = time.time()
            
        # Persist state and handle TP/SL placement
        asyncio.create_task(self._handle_fill_followup(order, fill_amount, fill_price))
        
    async def _handle_fill_followup(self, order: OrderState, fill_amount: Decimal, fill_price: Decimal):
        """Handle post-fill actions like TP/SL placement."""
        await self.state_manager.save_order(order)
        
        # Place TP/SL for filled portion
        if order.side == "BUY" and fill_amount > 0:
            await self._place_tp_sl_orders(fill_amount, fill_price)
            
    async def _place_tp_sl_orders(self, amount: Decimal, entry_price: Decimal):
        """Place take-profit and stop-loss orders."""
        try:
            # Calculate TP price with fees
            tp_price = compute_tp_price_with_fees(
                entry_price, 
                self.config.tp_spread, 
                self.config.maker_fee
            )
            
            # Place TP order
            tp_order_id = await self.place_order(
                is_buy=False,
                amount=amount,
                price=tp_price,
                order_type=OrderType.LIMIT
            )
            
            # Calculate SL price
            sl_price = entry_price * (Decimal("1") - self.config.sl_spread)
            
            # Attempt to place SL order (may not be supported on all exchanges)
            try:
                sl_order_id = await self.place_order(
                    is_buy=False,
                    amount=amount,
                    price=sl_price,
                    order_type=OrderType.LIMIT_MAKER  # Use limit maker for better compatibility
                )
                
                if not sl_order_id:
                    logger.info("Stop-loss order not supported, will use watchdog mechanism")
                    
            except Exception as e:
                logger.info(f"SL order failed, using watchdog: {e}")
                
        except Exception as e:
            logger.error(f"Failed to place TP/SL orders: {e}")
            
    def _on_order_completed(self, event):
        """Handle order completion events."""
        client_order_id = getattr(event, 'client_order_id', None)
        if client_order_id and client_order_id in self._orders:
            with self._lock:
                self._orders[client_order_id].status = OrderStatus.FILLED
                self._orders[client_order_id].updated_at = time.time()
            asyncio.create_task(self.state_manager.save_order(self._orders[client_order_id]))
            
    def _on_order_cancelled(self, event):
        """Handle order cancellation events."""
        client_order_id = getattr(event, 'client_order_id', None)
        if client_order_id and client_order_id in self._orders:
            with self._lock:
                self._orders[client_order_id].status = OrderStatus.CANCELED
                self._orders[client_order_id].updated_at = time.time()
            asyncio.create_task(self.state_manager.save_order(self._orders[client_order_id]))
            
    def _on_order_expired(self, event):
        """Handle order expiration events."""
        client_order_id = getattr(event, 'client_order_id', None)  
        if client_order_id and client_order_id in self._orders:
            with self._lock:
                self._orders[client_order_id].status = OrderStatus.CANCELED
                self._orders[client_order_id].updated_at = time.time()
            asyncio.create_task(self.state_manager.save_order(self._orders[client_order_id]))
            
    def _on_order_failed(self, event):
        """Handle order failure events."""
        client_order_id = getattr(event, 'client_order_id', None)
        if client_order_id and client_order_id in self._orders:
            with self._lock:
                self._orders[client_order_id].status = OrderStatus.FAILED
                self._orders[client_order_id].updated_at = time.time()
            asyncio.create_task(self.state_manager.save_order(self._orders[client_order_id]))
            
    # Public interface
    def get_active_orders(self) -> Dict[str, OrderState]:
        """Get all active orders."""
        with self._lock:
            return {k: v for k, v in self._orders.items() if v.is_active}
            
    def get_order_state(self, client_order_id: str) -> Optional[OrderState]:
        """Get order state by client ID."""
        return self._orders.get(client_order_id)
