"""Enhanced utilities with production-grade helpers."""
from decimal import Decimal, getcontext, ROUND_DOWN
from typing import Tuple
import time

# Set high precision for financial calculations
getcontext().prec = 28

def split_trading_pair(pair: str) -> Tuple[str, str]:
    """Split trading pair into base and quote assets."""
    separators = ["-", "/", "_"]
    for sep in separators:
        if sep in pair:
            parts = pair.split(sep, 1)
            if len(parts) == 2:
                return parts[0], parts[1]
    raise ValueError(f"Unable to split trading pair: {pair}")

def compute_tp_price_with_fees(entry_price: Decimal, tp_spread: Decimal, maker_fee: Decimal) -> Decimal:
    """Calculate take-profit price accounting for fees.
    
    Solve for TP where: (TP * (1 - fee)) - (entry * (1 + fee)) >= entry * tp_spread
    Rearranged: TP >= (entry * (1 + tp_spread + fee)) / (1 - fee)
    """
    numerator = entry_price * (Decimal("1") + tp_spread + maker_fee)
    denominator = Decimal("1") - maker_fee
    return (numerator / denominator).quantize(Decimal('0.********'), rounding=ROUND_DOWN)

def calculate_position_size(available_balance: Decimal, 
                          price: Decimal, 
                          risk_pct: Decimal, 
                          max_position: Decimal) -> Decimal:
    """Calculate optimal position size based on available balance and risk parameters."""
    # Size based on risk percentage
    risk_amount = available_balance * risk_pct
    size_by_risk = risk_amount / price
    
    # Enforce maximum position limit
    return min(size_by_risk, max_position).quantize(Decimal('0.********'), rounding=ROUND_DOWN)

def format_timestamp(timestamp: float) -> str:
    """Format timestamp for logging."""
    return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))

def calculate_pnl(entry_price: Decimal, 
                 exit_price: Decimal, 
                 amount: Decimal, 
                 is_long: bool,
                 fees: Decimal = Decimal("0")) -> Decimal:
    """Calculate P&L for a trade."""
    if is_long:
        gross_pnl = (exit_price - entry_price) * amount
    else:
        gross_pnl = (entry_price - exit_price) * amount
        
    return gross_pnl - fees

class PerformanceTracker:
    """Simple performance tracking utility."""
    
    def __init__(self):
        self.start_time = time.time()
        self.operation_times = {}
        
    def time_operation(self, operation_name: str):
        """Context manager for timing operations."""
        class TimingContext:
            def __init__(self, tracker, name):
                self.tracker = tracker
                self.name = name
                self.start = None
                
            def __enter__(self):
                self.start = time.time()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                duration = time.time() - self.start
                if self.name not in self.tracker.operation_times:
                    self.tracker.operation_times[self.name] = []
                self.tracker.operation_times[self.name].append(duration)
                
        return TimingContext(self, operation_name)
        
    def get_stats(self) -> dict:
        """Get performance statistics."""
        stats = {}
        for op_name, times in self.operation_times.items():
            stats[op_name] = {
                'count': len(times),
                'total_time': sum(times),
                'avg_time': sum(times) / len(times) if times else 0,
                'max_time': max(times) if times else 0
            }
        return stats
